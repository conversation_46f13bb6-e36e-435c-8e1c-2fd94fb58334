#!/bin/bash

# 启动天气MCP服务器
echo "启动天气MCP服务器..."

# 停止现有的天气MCP服务器进程
echo "检查并停止现有的天气MCP服务器..."
PID_8081=$(lsof -ti:8081)
if [ ! -z "$PID_8081" ]; then
    echo "发现端口8081上的进程 (PID: $PID_8081)，正在停止..."
    kill -9 $PID_8081
    sleep 2
    echo "已停止端口8081上的服务"
else
    echo "端口8081没有运行的服务"
fi

# 进入weather-mcp-server目录
cd weather-mcp-server

# 检查JAR文件
JAR_FILE="target/weather-mcp-server-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "JAR文件不存在，正在构建..."
    mvn clean package -DskipTests
fi

# 设置环境变量
export OPENWEATHER_API_KEY="********************************"

# 启动天气MCP服务器
echo "正在启动天气MCP服务器 (端口8081)..."
java -Dloader.main=com.example.aiagent.mcp.server.WeatherMcpServerApplication \
     -jar "$JAR_FILE" \
     --server.port=8081 \
     --spring.application.name=weather-mcp-server