#!/usr/bin/env python3
"""
简单的 MCP 服务器
提供基本的工具调用功能，用于测试 AI Agent
"""

import json
import time
import uuid
import requests
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class McpHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理 GET 请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/mcp/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'UP',
                'service': 'Simple MCP Server',
                'timestamp': datetime.now().isoformat(),
                'tools': ['get_weather', 'calculate', 'get_time']
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif path == '/mcp/tools':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            tools = self.get_available_tools()
            response = {
                'jsonrpc': '2.0',
                'result': {
                    'tools': tools
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def do_POST(self):
        """处理 POST 请求"""
        if self.path == '/mcp' or self.path == '/mcp/call':
            self.handle_mcp_request()
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def handle_mcp_request(self):
        """处理 MCP 请求"""
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))
            
            method = request_data.get('method')
            params = request_data.get('params', {})
            request_id = request_data.get('id')
            
            print(f"📨 收到 MCP 请求: {method}")
            
            # 处理不同的方法
            if method == 'tools/list':
                result = {'tools': self.get_available_tools()}
            elif method == 'tools/call':
                result = self.call_tool(params)
            else:
                result = {'error': f'Unknown method: {method}'}
            
            # 返回响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                'jsonrpc': '2.0',
                'id': request_id,
                'result': result
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'jsonrpc': '2.0',
                'id': request_data.get('id') if 'request_data' in locals() else None,
                'error': {'code': -32603, 'message': f'Internal error: {str(e)}'}
            }
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def get_available_tools(self):
        """获取可用工具列表"""
        return [
            {
                'name': 'get_weather',
                'description': '获取指定城市的天气信息',
                'inputSchema': {
                    'type': 'object',
                    'properties': {
                        'city': {
                            'type': 'string',
                            'description': '城市名称'
                        }
                    },
                    'required': ['city']
                }
            },
            {
                'name': 'calculate',
                'description': '执行数学计算',
                'inputSchema': {
                    'type': 'object',
                    'properties': {
                        'expression': {
                            'type': 'string',
                            'description': '数学表达式，如 "2 + 3" 或 "10 * 5"'
                        }
                    },
                    'required': ['expression']
                }
            },
            {
                'name': 'get_time',
                'description': '获取当前时间',
                'inputSchema': {
                    'type': 'object',
                    'properties': {
                        'timezone': {
                            'type': 'string',
                            'description': '时区（可选）',
                            'default': 'Asia/Shanghai'
                        }
                    }
                }
            }
        ]
    
    def call_tool(self, params):
        """调用工具"""
        tool_name = params.get('name')
        arguments = params.get('arguments', {})
        
        print(f"🔧 调用工具: {tool_name}, 参数: {arguments}")
        
        if tool_name == 'get_weather':
            return self.get_weather(arguments)
        elif tool_name == 'calculate':
            return self.calculate(arguments)
        elif tool_name == 'get_time':
            return self.get_time(arguments)
        else:
            return {
                'isError': True,
                'content': [{'type': 'text', 'text': f'Unknown tool: {tool_name}'}]
            }
    
    def get_weather(self, arguments):
        """获取天气信息"""
        city = arguments.get('city', '北京')
        
        # 模拟天气数据（实际应用中应该调用真实的天气 API）
        weather_data = {
            '北京': {'temperature': 15, 'condition': '晴天', 'humidity': 45},
            '上海': {'temperature': 18, 'condition': '多云', 'humidity': 60},
            '广州': {'temperature': 25, 'condition': '小雨', 'humidity': 80},
            '深圳': {'temperature': 24, 'condition': '晴天', 'humidity': 55}
        }
        
        weather = weather_data.get(city, {
            'temperature': 20, 
            'condition': '未知', 
            'humidity': 50
        })
        
        result_text = f"🌤️ {city}天气信息：\n" \
                     f"温度：{weather['temperature']}°C\n" \
                     f"天气：{weather['condition']}\n" \
                     f"湿度：{weather['humidity']}%\n" \
                     f"查询时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return {
            'content': [{'type': 'text', 'text': result_text}]
        }
    
    def calculate(self, arguments):
        """执行计算"""
        expression = arguments.get('expression', '')
        
        try:
            # 简单的数学表达式计算（安全起见，只允许基本运算）
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                raise ValueError("表达式包含不允许的字符")
            
            result = eval(expression)
            result_text = f"🧮 计算结果：\n{expression} = {result}"
            
            return {
                'content': [{'type': 'text', 'text': result_text}]
            }
        except Exception as e:
            return {
                'isError': True,
                'content': [{'type': 'text', 'text': f'计算错误: {str(e)}'}]
            }
    
    def get_time(self, arguments):
        """获取当前时间"""
        timezone = arguments.get('timezone', 'Asia/Shanghai')
        
        current_time = datetime.now()
        result_text = f"🕐 当前时间：\n" \
                     f"日期：{current_time.strftime('%Y年%m月%d日')}\n" \
                     f"时间：{current_time.strftime('%H:%M:%S')}\n" \
                     f"时区：{timezone}\n" \
                     f"时间戳：{int(current_time.timestamp())}"
        
        return {
            'content': [{'type': 'text', 'text': result_text}]
        }
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_mcp_server():
    """启动 MCP 服务器"""
    server_address = ('', 8081)
    httpd = HTTPServer(server_address, McpHandler)
    
    print("🚀 简单 MCP 服务器启动成功！")
    print("")
    print("📋 服务信息:")
    print("  地址: http://localhost:8081")
    print("  协议: MCP (Model Context Protocol)")
    print("")
    print("🧪 测试 URL:")
    print("  健康检查: http://localhost:8081/mcp/health")
    print("  工具列表: http://localhost:8081/mcp/tools")
    print("")
    print("📝 MCP 调用测试:")
    print("  curl -X POST http://localhost:8081/mcp \\")
    print("    -H 'Content-Type: application/json' \\")
    print("    -d '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"tools/list\"}'")
    print("")
    print("  curl -X POST http://localhost:8081/mcp \\")
    print("    -H 'Content-Type: application/json' \\")
    print("    -d '{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/call\",\"params\":{\"name\":\"get_weather\",\"arguments\":{\"city\":\"北京\"}}}'")
    print("")
    print("⏹️  按 Ctrl+C 停止服务")
    print("")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 MCP 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_mcp_server()
