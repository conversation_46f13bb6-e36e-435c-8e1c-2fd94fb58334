#!/bin/bash

echo "🧪 测试数据库连接和简单 API 功能..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 1. 测试 PostgreSQL 连接
echo "🔍 测试 PostgreSQL 连接..."
if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status, version();" 2>/dev/null; then
    echo "✅ PostgreSQL 连接正常"
else
    echo "❌ PostgreSQL 连接失败，正在启动服务..."
    brew services start postgresql@15
    sleep 3
    
    if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status;" 2>/dev/null; then
        echo "✅ PostgreSQL 服务启动成功"
    else
        echo "❌ PostgreSQL 服务启动失败"
        exit 1
    fi
fi

# 2. 测试数据库表创建
echo ""
echo "🗄️ 测试数据库表操作..."
psql testdb << 'EOF'
-- 创建测试表
DROP TABLE IF EXISTS test_conversations;
CREATE TABLE test_conversations (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO test_conversations (session_id, message, response) VALUES 
('test-session-1', '你好', '你好！我是 AI Agent，很高兴为您服务！'),
('test-session-1', '今天天气怎么样？', '抱歉，我需要天气服务来获取天气信息。');

-- 查询测试数据
SELECT 'Database operations successful' as status;
SELECT * FROM test_conversations;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 数据库表操作成功"
else
    echo "❌ 数据库表操作失败"
    exit 1
fi

# 3. 创建简单的 Spring Boot 测试应用
echo ""
echo "🚀 创建简单的测试应用..."

# 创建临时测试目录
TEST_DIR="temp-test-app"
rm -rf $TEST_DIR
mkdir -p $TEST_DIR/src/main/java/com/test
mkdir -p $TEST_DIR/src/main/resources

# 创建简单的 pom.xml
cat > $TEST_DIR/pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.test</groupId>
    <artifactId>simple-test-app</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>
    
    <properties>
        <java.version>17</java.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
EOF

# 创建简单的应用配置
cat > $TEST_DIR/src/main/resources/application.yml << 'EOF'
spring:
  application:
    name: simple-test-app
  datasource:
    url: ***************************************
    username: sunyilin
    password:
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
server:
  port: 8090
EOF

# 创建简单的主应用类
cat > $TEST_DIR/src/main/java/com/test/SimpleTestApp.java << 'EOF'
package com.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.Map;
import java.util.List;

@SpringBootApplication
@RestController
public class SimpleTestApp {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public static void main(String[] args) {
        SpringApplication.run(SimpleTestApp.class, args);
    }
    
    @GetMapping("/")
    public Map<String, String> home() {
        return Map.of(
            "status", "success",
            "message", "Simple Test App is running!",
            "timestamp", String.valueOf(System.currentTimeMillis())
        );
    }
    
    @GetMapping("/health")
    public Map<String, Object> health() {
        try {
            // 测试数据库连接
            String dbVersion = jdbcTemplate.queryForObject(
                "SELECT version()", String.class);
            
            return Map.of(
                "status", "UP",
                "database", "Connected",
                "version", dbVersion.substring(0, Math.min(50, dbVersion.length()))
            );
        } catch (Exception e) {
            return Map.of(
                "status", "DOWN",
                "database", "Disconnected",
                "error", e.getMessage()
            );
        }
    }
    
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody Map<String, String> request) {
        String message = request.getOrDefault("message", "");
        String sessionId = request.getOrDefault("sessionId", "default-session");
        
        if (message.isEmpty()) {
            return Map.of(
                "success", false,
                "error", "Message cannot be empty"
            );
        }
        
        try {
            // 保存到数据库
            jdbcTemplate.update(
                "INSERT INTO test_conversations (session_id, message, response) VALUES (?, ?, ?)",
                sessionId, message, "Echo: " + message
            );
            
            return Map.of(
                "success", true,
                "response", "Echo: " + message,
                "sessionId", sessionId,
                "timestamp", System.currentTimeMillis()
            );
        } catch (Exception e) {
            return Map.of(
                "success", false,
                "error", "Database error: " + e.getMessage()
            );
        }
    }
    
    @GetMapping("/conversations")
    public Map<String, Object> getConversations() {
        try {
            List<Map<String, Object>> conversations = jdbcTemplate.queryForList(
                "SELECT * FROM test_conversations ORDER BY created_at DESC LIMIT 10"
            );
            
            return Map.of(
                "success", true,
                "conversations", conversations
            );
        } catch (Exception e) {
            return Map.of(
                "success", false,
                "error", "Database error: " + e.getMessage()
            );
        }
    }
}
EOF

echo "✅ 简单测试应用创建完成"

# 4. 编译并启动测试应用
echo ""
echo "🏗️ 编译测试应用..."
cd $TEST_DIR

if mvn clean compile -q; then
    echo "✅ 测试应用编译成功"
    
    echo ""
    echo "🚀 启动测试应用..."
    echo "📝 应用将在端口 8090 启动"
    
    # 在后台启动应用
    nohup mvn spring-boot:run > ../test-app.log 2>&1 &
    TEST_APP_PID=$!
    
    echo "🔄 应用启动中，PID: $TEST_APP_PID"
    echo "📝 日志文件: test-app.log"
    
    # 等待应用启动
    echo "⏳ 等待应用启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8090/health >/dev/null 2>&1; then
            echo "✅ 测试应用启动成功！"
            break
        fi
        
        if [ $i -eq 30 ]; then
            echo "❌ 应用启动超时"
            echo "📝 查看日志："
            tail -20 ../test-app.log
            kill $TEST_APP_PID 2>/dev/null
            cd ..
            rm -rf $TEST_DIR
            exit 1
        fi
        
        if [ $((i % 5)) -eq 0 ]; then
            echo "等待中... ($i/30)"
        fi
        sleep 1
    done
    
    cd ..
    
    # 5. 测试 API 功能
    echo ""
    echo "🧪 测试 API 功能..."
    
    echo "📝 测试健康检查..."
    health_response=$(curl -s http://localhost:8090/health)
    echo "健康状态: $health_response"
    
    echo ""
    echo "📝 测试聊天功能..."
    chat_response=$(curl -s -X POST http://localhost:8090/chat \
      -H "Content-Type: application/json" \
      -d '{"message": "你好，这是一个测试消息", "sessionId": "test-session-123"}')
    echo "聊天响应: $chat_response"
    
    echo ""
    echo "📝 测试对话历史..."
    conversations_response=$(curl -s http://localhost:8090/conversations)
    echo "对话历史: $conversations_response"
    
    echo ""
    echo "🎉 所有测试完成！"
    echo ""
    echo "📋 测试结果总结："
    echo "  ✅ PostgreSQL 数据库连接正常"
    echo "  ✅ 数据库表操作成功"
    echo "  ✅ Spring Boot 应用编译成功"
    echo "  ✅ 应用启动成功 (端口 8090)"
    echo "  ✅ API 功能测试通过"
    echo ""
    echo "🔧 管理命令："
    echo "  查看日志: tail -f test-app.log"
    echo "  停止应用: kill $TEST_APP_PID"
    echo "  清理文件: rm -rf $TEST_DIR test-app.log"
    echo ""
    echo "🧪 测试 URL："
    echo "  健康检查: curl http://localhost:8090/health"
    echo "  发送消息: curl -X POST http://localhost:8090/chat -H 'Content-Type: application/json' -d '{\"message\":\"测试\",\"sessionId\":\"test\"}'"
    echo "  查看历史: curl http://localhost:8090/conversations"
    echo ""
    echo "💡 应用在后台运行，PID: $TEST_APP_PID"
    
else
    echo "❌ 测试应用编译失败"
    cd ..
    rm -rf $TEST_DIR
    exit 1
fi
