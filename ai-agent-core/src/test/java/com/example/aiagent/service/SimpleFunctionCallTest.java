package com.example.aiagent.service;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

/**
 * 简化的 Function Calling 测试，直接调用阿里云 API
 */
@SpringBootTest
public class SimpleFunctionCallTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleFunctionCallTest.class);
    
    @Test
    void testDirectApiCall() {
        logger.info("=== 直接测试阿里云 API ===");
        
        String apiUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
        String apiKey = "sk-fc81d268ace64286a752e8ef084144cc";
        
        // 构建请求体
        String requestBody = """
        {
            "model": "qwen-plus",
            "messages": [
                {"role": "user", "content": "请查询北京的天气"}
            ],
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "description": "查询指定城市的天气信息",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "城市名称"
                                }
                            },
                            "required": ["location"]
                        }
                    }
                }
            ],
            "tool_choice": "auto"
        }
        """;
        
        try {
            RestTemplate restTemplate = new RestTemplate();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            
            logger.info("发送请求到: {}", apiUrl);
            logger.info("请求体: {}", requestBody);
            
            String response = restTemplate.postForObject(apiUrl, entity, String.class);
            
            logger.info("=== API 响应 ===");
            logger.info("响应: {}", response);
            
            // 检查响应中是否包含工具调用
            if (response.contains("tool_calls")) {
                logger.info("✅ 成功！响应包含 tool_calls");
            } else {
                logger.warn("❌ 响应不包含 tool_calls");
            }
            
        } catch (Exception e) {
            logger.error("API 调用失败: {}", e.getMessage(), e);
        }
    }
}