package com.example.aiagent.service;

import com.example.aiagent.config.AiModelConfig;
import com.example.aiagent.model.*;
import com.example.aiagent.service.impl.AlibabaAiModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 专门测试阿里云大模型 Function Calling 功能的单元测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
public class AlibabaAiModelFunctionCallTest {
    
    private static final Logger logger = LoggerFactory.getLogger(AlibabaAiModelFunctionCallTest.class);
    
    private AlibabaAiModelService aiModelService;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        // 创建配置
        AiModelConfig config = new AiModelConfig();
        config.setProvider("alibaba");
        config.setApiKey("sk-fc81d268ace64286a752e8ef084144cc");
        config.setEndpoint("https://dashscope.aliyuncs.com/compatible-mode/v1");
        config.setModelName("qwen-plus");
        config.setMaxTokens(4000);
        config.setTemperature(0.7);
        config.setTimeoutSeconds(30);
        
        objectMapper = new ObjectMapper();
        aiModelService = new AlibabaAiModelService(config, objectMapper);
    }
    
    @Test
    void testFunctionCallWithWeatherTool() {
        logger.info("=== 开始测试 Function Calling ===");
        
        // 1. 创建工具定义
        ToolDefinition weatherTool = createWeatherToolDefinition();
        List<ToolDefinition> tools = Arrays.asList(weatherTool);
        
        // 2. 创建聊天请求
        ChatRequest request = new ChatRequest();
        request.setModel("qwen-plus");
        request.setMaxTokens(1000);
        request.setTemperature(0.7);
        request.setTools(tools);
        
        // 3. 创建消息
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("user", "请查询北京的天气"));
        request.setMessages(messages);
        
        logger.info("=== 发送请求 ===");
        logger.info("工具数量: {}", tools.size());
        logger.info("工具名称: {}", weatherTool.getFunction().getName());
        
        try {
            // 4. 发送请求
            ChatResponse response = aiModelService.chat(request);
            
            // 5. 验证响应
            assertNotNull(response, "响应不应为空");
            assertNotNull(response.getChoices(), "choices 不应为空");
            assertFalse(response.getChoices().isEmpty(), "choices 不应为空列表");
            
            ChatResponse.Choice choice = response.getChoices().get(0);
            ChatMessage message = choice.getMessage();
            
            logger.info("=== 响应分析 ===");
            logger.info("响应ID: {}", response.getId());
            logger.info("模型: {}", response.getModel());
            logger.info("用量: {}", response.getUsage());
            logger.info("消息角色: {}", message.getRole());
            logger.info("消息内容: '{}'", message.getContent());
            logger.info("工具调用: {}", message.getToolCalls());
            logger.info("结束原因: {}", choice.getFinishReason());
            
            // 6. 关键验证：检查是否有工具调用
            if (message.getToolCalls() != null && !message.getToolCalls().isEmpty()) {
                logger.info("✅ 成功！AI 模型返回了工具调用");
                
                for (ToolCall toolCall : message.getToolCalls()) {
                    logger.info("工具调用ID: {}", toolCall.getId());
                    logger.info("工具名称: {}", toolCall.getFunction().getName());
                    logger.info("工具参数: {}", toolCall.getFunction().getArguments());
                    
                    // 验证工具调用
                    assertEquals("get_weather", toolCall.getFunction().getName());
                    assertNotNull(toolCall.getFunction().getArguments());
                }
            } else {
                logger.error("❌ 失败！AI 模型没有返回工具调用");
                logger.error("这可能的原因：");
                logger.error("1. 模型不支持 function calling");
                logger.error("2. 工具定义格式错误");
                logger.error("3. 请求格式不正确");
                logger.error("4. API 配置问题");
                
                // 打印完整响应用于调试
                try {
                    String responseJson = objectMapper.writeValueAsString(response);
                    logger.error("完整响应: {}", responseJson);
                } catch (Exception e) {
                    logger.error("无法序列化响应: {}", e.getMessage());
                }
                
                fail("AI 模型应该返回工具调用，但实际没有");
            }
            
        } catch (Exception e) {
            logger.error("测试失败: {}", e.getMessage(), e);
            fail("Function Calling 测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testRequestFormat() {
        logger.info("=== 测试请求格式 ===");
        
        // 创建简单的工具定义
        ToolDefinition tool = createWeatherToolDefinition();
        
        ChatRequest request = new ChatRequest();
        request.setModel("qwen-plus");
        request.setTools(Arrays.asList(tool));
        request.setMessages(Arrays.asList(new ChatMessage("user", "测试")));
        
        try {
            String requestJson = objectMapper.writeValueAsString(request);
            logger.info("请求JSON: {}", requestJson);
            
            // 验证 JSON 格式
            assertTrue(requestJson.contains("tools"), "请求应包含 tools 字段");
            assertTrue(requestJson.contains("get_weather"), "请求应包含工具名称");
            assertTrue(requestJson.contains("function"), "请求应包含 function 字段");
            
        } catch (Exception e) {
            fail("请求序列化失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建天气工具定义
     */
    private ToolDefinition createWeatherToolDefinition() {
        ToolDefinition tool = new ToolDefinition();
        tool.setType("function");
        
        ToolDefinition.Function function = new ToolDefinition.Function();
        function.setName("get_weather");
        function.setDescription("查询指定城市的天气信息");
        
        // 创建参数定义
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> locationParam = new HashMap<>();
        locationParam.put("type", "string");
        locationParam.put("description", "城市名称，例如：北京、上海");
        properties.put("location", locationParam);
        
        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("location"));
        
        function.setParameters(parameters);
        tool.setFunction(function);
        
        return tool;
    }
}