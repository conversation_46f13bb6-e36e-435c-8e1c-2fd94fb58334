spring:
  application:
    name: ai-agent-framework

  # PostgreSQL数据库配置 (本地安装)
  datasource:
    url: ***************************************
    username: sunyilin
    password: sunyilin
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000

  # JPA配置主机: localhost (127.0.0.1)
  #端口: 5432
  #数据库名: testdb
  #用户名: sunyilin
  #密码: (无密码/空密码)
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # Flyway数据库迁移配置
  flyway:
    enabled: false
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  profiles:
    active: dev

logging:
  level:
    com.example.aiagent: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
spring:
  config:
    activate:
      on-profile: dev

# 大模型配置 - 使用您的阿里云配置
ai:
  model:
    provider: alibaba
    api-key: sk-fc81d268ace64286a752e8ef084144cc
    endpoint: https://dashscope.aliyuncs.com/compatible-mode/v1
    model-name: qwen-plus  # 改为 qwen-plus
    max-tokens: 4000
    temperature: 0.7
    timeout-seconds: 30
  
  # Agent配置
  agent:
    max-iterations: 10
    timeout-seconds: 60
    enable-mcp: true
  
  # MCP配置
  mcp:
    enabled: true
    servers:
      - name: "filesystem"
        endpoint: "stdio://npx @modelcontextprotocol/server-filesystem /path/to/allowed"
        protocol: "stdio"
        capabilities:
          tools: "true"
          resources: "true"
      - name: "web-search"
        endpoint: "stdio://npx @modelcontextprotocol/server-web-search"
        protocol: "stdio"
        capabilities:
          tools: "true"
      - name: "weather"
        endpoint: "http://localhost:8081/mcp"
        protocol: "http"
        capabilities:
          tools: "true"
    # 移除原来的weather配置，改用MCP服务器
    # weather:
    #   url: "https://api.openweathermap.org/data/2.5"
    #   api-key: "${OPENWEATHER_API_KEY:********************************}"
    #   enabled: true
  
  # 知识库配置
  knowledge:
    enabled: true
    sources:
      - name: "vector-store"
        type: "memory"
        settings:
          dimension: 1536
          index-type: "flat"
    max-results: 5
    similarity-threshold: 0.7

---
spring:
  config:
    activate:
      on-profile: test

ai:
  model:
    provider: alibaba
    api-key: "sk-fc81d268ace64286a752e8ef084144cc"
    endpoint: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model-name: "test-model"
    max-tokens: 1000
    temperature: 0.0
    timeout-seconds: 10
  mcp:
    enabled: false
  knowledge:
    enabled: false