-- 创建对话历史记录表
CREATE TABLE conversation_history (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(100),
    user_role VARCHAR(50),
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('USER', 'ASSISTANT', 'SYSTEM', 'TOOL')),
    content TEXT,
    tool_calls TEXT,
    tool_call_id VARCHAR(100),
    cognitive_architecture VARCHAR(50),
    processing_mode VARCHAR(50),
    response_time_ms BIGINT,
    token_usage TEXT,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT
);

-- 创建索引以提高查询性能
CREATE INDEX idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX idx_conversation_history_user_id ON conversation_history(user_id);
CREATE INDEX idx_conversation_history_created_at ON conversation_history(created_at);
CREATE INDEX idx_conversation_history_message_type ON conversation_history(message_type);
CREATE INDEX idx_conversation_history_user_created ON conversation_history(user_id, created_at DESC);
CREATE INDEX idx_conversation_history_session_created ON conversation_history(session_id, created_at ASC);

-- 创建复合索引
CREATE INDEX idx_conversation_history_user_session ON conversation_history(user_id, session_id);
CREATE INDEX idx_conversation_history_session_type ON conversation_history(session_id, message_type);

-- 添加表注释
COMMENT ON TABLE conversation_history IS '对话历史记录表';
COMMENT ON COLUMN conversation_history.id IS '主键ID';
COMMENT ON COLUMN conversation_history.session_id IS '会话ID，用于关联同一次对话的多轮交互';
COMMENT ON COLUMN conversation_history.user_id IS '用户ID';
COMMENT ON COLUMN conversation_history.user_role IS '用户角色';
COMMENT ON COLUMN conversation_history.message_type IS '消息类型：USER-用户消息，ASSISTANT-AI助手消息，SYSTEM-系统消息，TOOL-工具调用结果';
COMMENT ON COLUMN conversation_history.content IS '消息内容';
COMMENT ON COLUMN conversation_history.tool_calls IS '工具调用信息（JSON格式）';
COMMENT ON COLUMN conversation_history.tool_call_id IS '工具调用ID';
COMMENT ON COLUMN conversation_history.cognitive_architecture IS '认知架构类型';
COMMENT ON COLUMN conversation_history.processing_mode IS '处理模式';
COMMENT ON COLUMN conversation_history.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN conversation_history.token_usage IS 'Token使用统计（JSON格式）';
COMMENT ON COLUMN conversation_history.error_message IS '错误信息';
COMMENT ON COLUMN conversation_history.created_at IS '创建时间';
COMMENT ON COLUMN conversation_history.updated_at IS '更新时间';
COMMENT ON COLUMN conversation_history.metadata IS '额外的元数据（JSON格式）';
