package com.example.aiagent.controller;

import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import com.example.aiagent.model.ToolDefinition;
import com.example.aiagent.function.FunctionExecutor;
import com.example.aiagent.service.AgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/agent")
public class AgentController {
    
    @Autowired
    private AgentService agentService;
    
    @Autowired
    private FunctionExecutor functionExecutor;
    
    @PostMapping("/chat")
    public AgentResponse chat(@RequestBody AgentRequest request) {
        return agentService.processRequest(request);
    }

    /**
     * 简化版聊天接口 - 只返回最终答案
     */
    @PostMapping("/chat/simple")
    public String chatSimple(@RequestBody AgentRequest request) {
        AgentResponse response = agentService.processRequest(request);
        return response.getContent();
    }

    /**
     * 灵活的聊天接口 - 支持通过参数控制返回格式
     */
    @PostMapping("/chat/flexible")
    public ResponseEntity<?> chatFlexible(
            @RequestBody AgentRequest request,
            @RequestParam(value = "format", defaultValue = "full") String format) {

        AgentResponse response = agentService.processRequest(request);

        switch (format.toLowerCase()) {
            case "simple":
            case "content":
                // 只返回最终答案内容
                return ResponseEntity.ok(response.getContent());

            case "minimal":
                // 返回最小化的JSON
                Map<String, Object> minimal = new HashMap<>();
                minimal.put("answer", response.getContent());
                minimal.put("success", response.isSuccess());
                return ResponseEntity.ok(minimal);

            case "full":
            default:
                // 返回完整的响应
                return ResponseEntity.ok(response);
        }
    }

    /**
     * 流式ReAct接口 - 持续输出思考过程
     */
    @PostMapping(value = "/chat/react-stream", produces = MediaType.TEXT_PLAIN_VALUE)
    public SseEmitter reactStream(@RequestBody AgentRequest request) {
        // 强制使用ReAct模式
        request.setMode(com.example.aiagent.model.AgentMode.REACT);

        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 异步处理
        new Thread(() -> {
            try {
                // 调用流式ReAct服务
                agentService.processRequestStream(request, emitter);
            } catch (Exception e) {
                try {
                    emitter.send("❌ 处理过程中发生错误: " + e.getMessage());
                    emitter.complete();
                } catch (Exception ex) {
                    emitter.completeWithError(ex);
                }
            }
        }).start();

        return emitter;
    }

    @GetMapping("/debug/tools")
    public ResponseEntity<?> getAvailableTools() {
        try {
            List<ToolDefinition> tools = functionExecutor.getAvailableTools();
            Map<String, Object> debug = new HashMap<>();
            debug.put("toolCount", tools.size());
            debug.put("tools", tools);
            return ResponseEntity.ok(debug);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }
}