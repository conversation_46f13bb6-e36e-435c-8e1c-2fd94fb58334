package com.example.aiagent.controller;

import com.example.aiagent.entity.ConversationHistory;
import com.example.aiagent.service.ConversationHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对话历史记录API控制器
 */
@RestController
@RequestMapping("/api/conversation")
@CrossOrigin(origins = "*")
public class ConversationHistoryController {
    
    @Autowired
    private ConversationHistoryService conversationHistoryService;
    
    /**
     * 获取会话历史
     */
    @GetMapping("/session/{sessionId}")
    public ResponseEntity<List<ConversationHistory>> getSessionHistory(@PathVariable String sessionId) {
        List<ConversationHistory> history = conversationHistoryService.getSessionHistory(sessionId);
        return ResponseEntity.ok(history);
    }
    
    /**
     * 获取用户对话历史（分页）
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<ConversationHistory>> getUserHistory(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Page<ConversationHistory> history = conversationHistoryService.getUserHistory(userId, page, size);
        return ResponseEntity.ok(history);
    }
    
    /**
     * 获取用户最近的对话记录
     */
    @GetMapping("/user/{userId}/recent")
    public ResponseEntity<List<ConversationHistory>> getRecentConversations(
            @PathVariable String userId,
            @RequestParam(defaultValue = "10") int limit) {
        List<ConversationHistory> conversations = conversationHistoryService.getRecentConversations(userId, limit);
        return ResponseEntity.ok(conversations);
    }
    
    /**
     * 获取用户的所有会话ID
     */
    @GetMapping("/user/{userId}/sessions")
    public ResponseEntity<List<String>> getUserSessionIds(@PathVariable String userId) {
        List<String> sessionIds = conversationHistoryService.getUserSessionIds(userId);
        return ResponseEntity.ok(sessionIds);
    }
    
    /**
     * 获取会话统计信息
     */
    @GetMapping("/session/{sessionId}/stats")
    public ResponseEntity<Map<String, Object>> getSessionStats(@PathVariable String sessionId) {
        long messageCount = conversationHistoryService.getSessionMessageCount(sessionId);
        List<ConversationHistory> history = conversationHistoryService.getSessionHistory(sessionId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("sessionId", sessionId);
        stats.put("messageCount", messageCount);
        stats.put("startTime", history.isEmpty() ? null : history.get(0).getCreatedAt());
        stats.put("endTime", history.isEmpty() ? null : history.get(history.size() - 1).getCreatedAt());
        
        // 统计消息类型分布
        Map<String, Long> messageTypeStats = new HashMap<>();
        history.forEach(h -> {
            String type = h.getMessageType().toString();
            messageTypeStats.put(type, messageTypeStats.getOrDefault(type, 0L) + 1);
        });
        stats.put("messageTypeStats", messageTypeStats);
        
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 获取用户在时间范围内的对话统计
     */
    @GetMapping("/user/{userId}/stats")
    public ResponseEntity<Map<String, Object>> getUserStats(
            @PathVariable String userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        long conversationCount = conversationHistoryService.getUserConversationCount(userId, startTime, endTime);
        List<String> sessionIds = conversationHistoryService.getUserSessionIds(userId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);
        stats.put("conversationCount", conversationCount);
        stats.put("sessionCount", sessionIds.size());
        stats.put("startTime", startTime);
        stats.put("endTime", endTime);
        
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 获取认知架构使用统计
     */
    @GetMapping("/stats/cognitive-architecture")
    public ResponseEntity<Map<String, Long>> getCognitiveArchitectureStats() {
        Map<String, Long> stats = conversationHistoryService.getCognitiveArchitectureStats();
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 搜索对话历史
     */
    @GetMapping("/search")
    public ResponseEntity<List<ConversationHistory>> searchConversations(
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String sessionId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) ConversationHistory.MessageType messageType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        // 这里可以实现更复杂的搜索逻辑
        // 目前先返回基本的会话历史
        if (sessionId != null) {
            List<ConversationHistory> history = conversationHistoryService.getSessionHistory(sessionId);
            return ResponseEntity.ok(history);
        } else if (userId != null) {
            Page<ConversationHistory> history = conversationHistoryService.getUserHistory(userId, page, size);
            return ResponseEntity.ok(history.getContent());
        }
        
        return ResponseEntity.ok(List.of());
    }
    
    /**
     * 删除会话历史
     */
    @DeleteMapping("/session/{sessionId}")
    public ResponseEntity<Map<String, String>> deleteSessionHistory(@PathVariable String sessionId) {
        // 这里需要实现删除逻辑
        Map<String, String> response = new HashMap<>();
        response.put("message", "会话历史删除功能待实现");
        response.put("sessionId", sessionId);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 清理过期的历史记录
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, String>> cleanupOldHistory(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime cutoffTime) {
        
        conversationHistoryService.cleanupOldHistory(cutoffTime);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "成功清理了 " + cutoffTime + " 之前的历史记录");
        response.put("cutoffTime", cutoffTime.toString());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats/system")
    public ResponseEntity<Map<String, Object>> getSystemStats() {
        Map<String, Long> cognitiveStats = conversationHistoryService.getCognitiveArchitectureStats();
        
        Map<String, Object> systemStats = new HashMap<>();
        systemStats.put("cognitiveArchitectureStats", cognitiveStats);
        systemStats.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(systemStats);
    }
}
