package com.example.aiagent.mcp.registry;

import com.example.aiagent.mcp.annotation.McpService;
import com.example.aiagent.mcp.annotation.McpTool;
import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.service.McpServiceInterface;
import com.example.aiagent.model.ToolDefinition;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态MCP服务注册器
 * 自动发现和注册带有@McpService注解的Spring服务
 */
@Component
public class DynamicMcpServiceRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicMcpServiceRegistry.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // 注册的服务映射：toolName -> ServiceInfo
    private final Map<String, ServiceInfo> registeredServices = new ConcurrentHashMap<>();
    
    // 工具定义缓存
    private final Map<String, ToolDefinition> toolDefinitions = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        logger.info("🔍 开始扫描MCP服务...");
        scanAndRegisterServices();
        logger.info("✅ MCP服务扫描完成，注册了 {} 个工具", registeredServices.size());
    }
    
    /**
     * 扫描并注册MCP服务
     */
    private void scanAndRegisterServices() {
        // 获取所有带有@McpService注解的Bean
        Map<String, Object> mcpServices = applicationContext.getBeansWithAnnotation(McpService.class);
        
        for (Map.Entry<String, Object> entry : mcpServices.entrySet()) {
            String beanName = entry.getKey();
            Object serviceBean = entry.getValue();
            
            logger.info("📋 发现MCP服务: {}", beanName);
            registerService(beanName, serviceBean);
        }
    }
    
    /**
     * 注册单个服务
     */
    private void registerService(String beanName, Object serviceBean) {
        Class<?> serviceClass = serviceBean.getClass();
        McpService mcpServiceAnnotation = serviceClass.getAnnotation(McpService.class);
        
        if (mcpServiceAnnotation == null) {
            return;
        }
        
        String serviceName = mcpServiceAnnotation.name().isEmpty() ? 
            beanName : mcpServiceAnnotation.name();
        
        logger.info("🔧 注册MCP服务: {} ({})", serviceName, serviceClass.getSimpleName());
        
        // 扫描服务中的工具方法
        Method[] methods = serviceClass.getDeclaredMethods();
        for (Method method : methods) {
            McpTool toolAnnotation = method.getAnnotation(McpTool.class);
            if (toolAnnotation != null) {
                registerTool(serviceName, serviceBean, method, toolAnnotation);
            }
        }
        
        // 初始化服务
        if (serviceBean instanceof McpServiceInterface) {
            ((McpServiceInterface) serviceBean).initialize();
        }
    }
    
    /**
     * 注册工具方法
     */
    private void registerTool(String serviceName, Object serviceBean, Method method, McpTool toolAnnotation) {
        String toolName = toolAnnotation.name().isEmpty() ? 
            serviceName + "_" + method.getName() : toolAnnotation.name();
        
        logger.info("🛠️ 注册工具: {} -> {}.{}", toolName, serviceName, method.getName());
        
        // 创建服务信息
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.serviceName = serviceName;
        serviceInfo.serviceBean = serviceBean;
        serviceInfo.method = method;
        serviceInfo.toolAnnotation = toolAnnotation;
        serviceInfo.requireAuth = toolAnnotation.requireAuth();
        serviceInfo.permissions = Arrays.asList(toolAnnotation.permissions());
        
        registeredServices.put(toolName, serviceInfo);
        
        // 生成工具定义
        ToolDefinition toolDefinition = generateToolDefinition(toolName, method, toolAnnotation);
        toolDefinitions.put(toolName, toolDefinition);
    }
    
    /**
     * 生成工具定义
     */
    private ToolDefinition generateToolDefinition(String toolName, Method method, McpTool toolAnnotation) {
        ToolDefinition toolDefinition = new ToolDefinition();
        
        // 设置基本信息
        ToolDefinition.Function function = new ToolDefinition.Function();
        function.setName(toolName);
        function.setDescription(toolAnnotation.description());
        
        // 生成参数Schema
        Map<String, Object> schema = generateParameterSchema(method);
        function.setParameters(schema);
        
        toolDefinition.setType("function");
        toolDefinition.setFunction(function);
        
        return toolDefinition;
    }
    
    /**
     * 生成参数Schema
     */
    private Map<String, Object> generateParameterSchema(Method method) {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        List<String> required = new ArrayList<>();
        
        Parameter[] parameters = method.getParameters();
        for (Parameter parameter : parameters) {
            // 跳过McpExecutionContext参数
            if (parameter.getType() == McpExecutionContext.class) {
                continue;
            }
            
            String paramName = parameter.getName();
            Map<String, Object> paramSchema = new HashMap<>();
            
            // 根据参数类型设置schema
            Class<?> paramType = parameter.getType();
            if (paramType == String.class) {
                paramSchema.put("type", "string");
            } else if (paramType == Integer.class || paramType == int.class) {
                paramSchema.put("type", "integer");
            } else if (paramType == Boolean.class || paramType == boolean.class) {
                paramSchema.put("type", "boolean");
            } else if (paramType == Double.class || paramType == double.class) {
                paramSchema.put("type", "number");
            } else {
                paramSchema.put("type", "object");
            }
            
            properties.put(paramName, paramSchema);
            required.add(paramName);
        }
        
        schema.put("properties", properties);
        schema.put("required", required);
        
        return schema;
    }
    
    /**
     * 获取所有工具定义
     */
    public List<ToolDefinition> getAllToolDefinitions() {
        return new ArrayList<>(toolDefinitions.values());
    }
    
    /**
     * 获取指定用户可访问的工具定义
     */
    public List<ToolDefinition> getToolDefinitionsForUser(McpExecutionContext context) {
        List<ToolDefinition> userTools = new ArrayList<>();
        
        for (Map.Entry<String, ServiceInfo> entry : registeredServices.entrySet()) {
            String toolName = entry.getKey();
            ServiceInfo serviceInfo = entry.getValue();
            
            if (hasPermissionToUseTool(context, serviceInfo)) {
                userTools.add(toolDefinitions.get(toolName));
            }
        }
        
        return userTools;
    }
    
    /**
     * 执行工具
     */
    public Object executeTool(String toolName, Map<String, Object> arguments, McpExecutionContext context) {
        ServiceInfo serviceInfo = registeredServices.get(toolName);
        if (serviceInfo == null) {
            throw new RuntimeException("工具不存在: " + toolName);
        }
        
        // 权限检查
        if (!hasPermissionToUseTool(context, serviceInfo)) {
            throw new RuntimeException("用户 " + context.getUserId() + " 没有权限使用工具: " + toolName);
        }
        
        try {
            // 准备方法参数
            Object[] methodArgs = prepareMethodArguments(serviceInfo.method, arguments, context);
            
            // 调用方法
            logger.info("🚀 执行工具: {} (用户: {})", toolName, context.getUserId());
            Object result = serviceInfo.method.invoke(serviceInfo.serviceBean, methodArgs);
            
            logger.info("✅ 工具执行成功: {}", toolName);
            return result;
            
        } catch (Exception e) {
            logger.error("❌ 工具执行失败: {} - {}", toolName, e.getMessage(), e);
            throw new RuntimeException("工具执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查用户是否有权限使用工具
     */
    private boolean hasPermissionToUseTool(McpExecutionContext context, ServiceInfo serviceInfo) {
        // 如果不需要认证，直接允许
        if (!serviceInfo.requireAuth) {
            return true;
        }
        
        // 检查用户是否已认证
        if (context.getUserId() == null) {
            return false;
        }
        
        // 检查权限
        if (!serviceInfo.permissions.isEmpty()) {
            return context.hasAnyPermission(serviceInfo.permissions.toArray(new String[0]));
        }
        
        // 检查服务级别权限
        if (serviceInfo.serviceBean instanceof McpServiceInterface) {
            return ((McpServiceInterface) serviceInfo.serviceBean).hasPermission(context);
        }
        
        return true;
    }
    
    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Map<String, Object> arguments, McpExecutionContext context) {
        Parameter[] parameters = method.getParameters();
        Object[] methodArgs = new Object[parameters.length];
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            
            if (parameter.getType() == McpExecutionContext.class) {
                // 注入执行上下文
                methodArgs[i] = context;
            } else {
                // 从参数中获取值
                String paramName = parameter.getName();
                Object value = arguments.get(paramName);
                methodArgs[i] = convertValue(value, parameter.getType());
            }
        }
        
        return methodArgs;
    }
    
    /**
     * 类型转换
     */
    private Object convertValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        // 简单的类型转换
        if (targetType == String.class) {
            return value.toString();
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(value.toString());
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.valueOf(value.toString());
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.valueOf(value.toString());
        }
        
        return value;
    }
    
    /**
     * 服务信息内部类
     */
    private static class ServiceInfo {
        String serviceName;
        Object serviceBean;
        Method method;
        McpTool toolAnnotation;
        boolean requireAuth;
        List<String> permissions;
    }
}
