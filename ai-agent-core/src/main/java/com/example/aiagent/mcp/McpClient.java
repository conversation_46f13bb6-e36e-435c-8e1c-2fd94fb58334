package com.example.aiagent.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP客户端
 * 用于与MCP服务器通信
 */
@Component
public class McpClient {

    private static final Logger logger = LoggerFactory.getLogger(McpClient.class);

    @Autowired
    private ObjectMapper objectMapper;

    private final WebClient webClient = WebClient.builder().build();

    // 记录已初始化的服务器
    private final Map<String, Boolean> initializedServers = new HashMap<>();
    
    /**
     * 调用MCP服务器的工具
     */
    public Object callTool(String serverUrl, String toolName, Object parameters) {
        logger.info("调用MCP工具: {} at {}", toolName, serverUrl);

        // 确保服务器已初始化
        ensureServerInitialized(serverUrl);

        try {
            // 构建 MCP 请求
            Map<String, Object> mcpRequest = new HashMap<>();
            mcpRequest.put("jsonrpc", "2.0");
            mcpRequest.put("id", System.currentTimeMillis());
            mcpRequest.put("method", "tools/call");
            
            Map<String, Object> params = new HashMap<>();
            params.put("name", toolName);
            params.put("arguments", parameters);
            mcpRequest.put("params", params);
            
            logger.info("发送MCP请求: {}", mcpRequest);
            
            // 发送 HTTP 请求
            Mono<String> response = webClient.post()
                .uri(serverUrl)
                .header("Content-Type", "application/json")
                .bodyValue(mcpRequest)
                .retrieve()
                .bodyToMono(String.class);
            
            String result = response.block();
            logger.info("MCP服务器响应: {}", result);
            
            // 解析响应
            Map<String, Object> responseMap = objectMapper.readValue(result, Map.class);
            if (responseMap.containsKey("result")) {
                return responseMap.get("result");
            } else if (responseMap.containsKey("error")) {
                throw new RuntimeException("MCP错误: " + responseMap.get("error"));
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("调用MCP服务器失败: {}", e.getMessage(), e);
            throw new RuntimeException("MCP调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 确保MCP服务器已初始化
     */
    private void ensureServerInitialized(String serverUrl) {
        if (!initializedServers.getOrDefault(serverUrl, false)) {
            logger.info("初始化MCP服务器: {}", serverUrl);
            initializeServer(serverUrl);
            initializedServers.put(serverUrl, true);
        }
    }

    /**
     * 初始化MCP服务器
     */
    private void initializeServer(String serverUrl) {
        try {
            // 发送初始化请求
            Map<String, Object> initRequest = new HashMap<>();
            initRequest.put("jsonrpc", "2.0");
            initRequest.put("id", System.currentTimeMillis());
            initRequest.put("method", "initialize");

            Map<String, Object> params = new HashMap<>();
            params.put("protocolVersion", "2024-11-05");

            Map<String, Object> clientInfo = new HashMap<>();
            clientInfo.put("name", "ai-agent-framework");
            clientInfo.put("version", "1.0.0");
            params.put("clientInfo", clientInfo);

            Map<String, Object> capabilities = new HashMap<>();
            capabilities.put("tools", Map.of("listChanged", false));
            params.put("capabilities", capabilities);

            initRequest.put("params", params);

            logger.info("发送初始化请求: {}", initRequest);

            Mono<String> response = webClient.post()
                .uri(serverUrl)
                .header("Content-Type", "application/json")
                .bodyValue(initRequest)
                .retrieve()
                .bodyToMono(String.class);

            String result = response.block();
            logger.info("初始化响应: {}", result);

            // 发送initialized通知
            Map<String, Object> notifyRequest = new HashMap<>();
            notifyRequest.put("jsonrpc", "2.0");
            notifyRequest.put("method", "notifications/initialized");
            notifyRequest.put("params", new HashMap<>());

            webClient.post()
                .uri(serverUrl)
                .header("Content-Type", "application/json")
                .bodyValue(notifyRequest)
                .retrieve()
                .bodyToMono(String.class)
                .block();

            logger.info("MCP服务器初始化完成: {}", serverUrl);

        } catch (Exception e) {
            logger.error("初始化MCP服务器失败: {}", e.getMessage(), e);
            throw new RuntimeException("MCP服务器初始化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取MCP服务器的工具列表
     */
    public Object getTools(String serverUrl) {
        logger.info("获取MCP服务器工具列表: {}", serverUrl);

        ensureServerInitialized(serverUrl);

        try {
            Map<String, Object> request = new HashMap<>();
            request.put("jsonrpc", "2.0");
            request.put("id", System.currentTimeMillis());
            request.put("method", "tools/list");
            request.put("params", new HashMap<>());

            Mono<String> response = webClient.post()
                .uri(serverUrl)
                .header("Content-Type", "application/json")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(String.class);

            String result = response.block();
            logger.info("工具列表响应: {}", result);

            Map<String, Object> responseMap = objectMapper.readValue(result, Map.class);
            return responseMap.get("result");

        } catch (Exception e) {
            logger.error("获取工具列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取工具列表失败: " + e.getMessage(), e);
        }
    }
}