package com.example.aiagent.mcp;

import com.example.aiagent.function.FunctionResult;
import com.example.aiagent.model.ToolCall;
import com.example.aiagent.service.CityNameMappingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.Map;

/**
 * MCP函数适配器
 * 将MCP服务器的工具适配为Agent框架的函数
 */
public class McpFunctionAdapter {
    
    private static final Logger logger = LoggerFactory.getLogger(McpFunctionAdapter.class);
    
    private final String serverUrl;
    private final String toolName;
    private final McpClient mcpClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ApplicationContext applicationContext;
    
    public McpFunctionAdapter(String serverUrl, String toolName, McpClient mcpClient) {
        this.serverUrl = serverUrl;
        this.toolName = toolName;
        this.mcpClient = mcpClient;
        this.applicationContext = null; // 将通过setter注入
    }

    public McpFunctionAdapter(String serverUrl, String toolName, McpClient mcpClient, ApplicationContext applicationContext) {
        this.serverUrl = serverUrl;
        this.toolName = toolName;
        this.mcpClient = mcpClient;
        this.applicationContext = applicationContext;
    }
    
    /**
     * 执行MCP工具调用
     */
    public FunctionResult execute(ToolCall toolCall) {
        try {
            logger.info("执行MCP工具: {} (服务器: {})", toolName, serverUrl);

            // 解析参数
            Object parameters = parseParameters(toolCall.getFunction().getArguments());

            // 如果是天气查询，进行城市名称映射
            if ("get_weather".equals(toolName) && parameters instanceof Map) {
                parameters = mapCityNameForWeather((Map<String, Object>) parameters);
            }

            // 调用MCP服务器
            Object result = mcpClient.callTool(serverUrl, toolName, parameters);

            return new FunctionResult("success", result.toString());

        } catch (Exception e) {
            logger.error("MCP工具调用失败: {}", e.getMessage(), e);
            return new FunctionResult("error", "MCP工具调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 为天气查询映射城市名称
     */
    private Map<String, Object> mapCityNameForWeather(Map<String, Object> parameters) {
        try {
            if (applicationContext != null && parameters.containsKey("city")) {
                CityNameMappingService mappingService = applicationContext.getBean(CityNameMappingService.class);
                String originalCity = (String) parameters.get("city");
                String mappedCity = mappingService.mapCityName(originalCity);

                if (!originalCity.equals(mappedCity)) {
                    logger.info("城市名称映射: {} -> {}", originalCity, mappedCity);
                    parameters.put("city", mappedCity);
                }
            }
        } catch (Exception e) {
            logger.warn("城市名称映射失败，使用原始名称: {}", e.getMessage());
        }
        return parameters;
    }

    private Object parseParameters(String arguments) {
        try {
            logger.info("解析参数: {}", arguments);
            // 将JSON字符串解析为Map对象
            Map<String, Object> params = objectMapper.readValue(arguments, Map.class);
            logger.info("解析后的参数: {}", params);
            return params;
        } catch (Exception e) {
            logger.error("参数解析失败: {}", e.getMessage(), e);
            // 如果解析失败，返回原始字符串
            return arguments;
        }
    }
    
    public String getToolName() {
        return toolName;
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
}