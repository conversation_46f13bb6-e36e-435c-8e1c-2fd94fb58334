package com.example.aiagent.mcp.service;

import com.example.aiagent.mcp.context.McpExecutionContext;

/**
 * MCP服务接口
 * 所有MCP服务都应该实现此接口（可选）
 */
public interface McpServiceInterface {
    
    /**
     * 服务初始化
     * 在服务注册时调用
     */
    default void initialize() {
        // 默认空实现
    }
    
    /**
     * 服务销毁
     * 在服务注销时调用
     */
    default void destroy() {
        // 默认空实现
    }
    
    /**
     * 检查用户是否有权限访问此服务
     * 
     * @param context 执行上下文
     * @return 是否有权限
     */
    default boolean hasPermission(McpExecutionContext context) {
        // 默认允许所有用户访问
        return true;
    }
    
    /**
     * 获取服务健康状态
     * 
     * @return 健康状态信息
     */
    default String getHealthStatus() {
        return "healthy";
    }
}
