package com.example.aiagent.mcp;

import com.example.aiagent.function.FunctionRegistry;
import com.example.aiagent.mcp.McpClient;
import com.example.aiagent.mcp.McpFunctionAdapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天气查询MCP服务集成
 * 通过MCP客户端调用本地天气MCP服务器
 * 只在主Agent应用中启用
 */
@Service
@ConditionalOnProperty(name = "spring.application.name", havingValue = "ai-agent-framework", matchIfMissing = true)
public class WeatherMcpService {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherMcpService.class);
    
    private static final String WEATHER_MCP_SERVER_URL = "http://localhost:8081/mcp";
    
    @Autowired
    private McpClient mcpClient;
    
    @Autowired
    private FunctionRegistry functionRegistry;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ApplicationContext applicationContext;
    
    @PostConstruct
    public void initializeWeatherFunctions() {
        logger.info("=== WeatherMcpService 开始初始化 ===");
        logger.info("应用名称: {}", System.getProperty("spring.application.name"));
        
        // 延迟初始化，确保 MCP 服务器已启动
        new Thread(() -> {
            try {
                // 等待 MCP 服务器启动
                Thread.sleep(3000);
                
                // 重试机制
                for (int i = 0; i < 5; i++) {
                    try {
                        registerWeatherMcpFunction();
                        logger.info("天气MCP服务初始化完成，连接到: {}", WEATHER_MCP_SERVER_URL);
                        return;
                    } catch (Exception e) {
                        logger.warn("天气MCP服务初始化失败，重试 {}/5: {}", i + 1, e.getMessage());
                        if (i < 4) {
                            Thread.sleep(2000);
                        }
                    }
                }
                logger.error("天气MCP服务初始化最终失败");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    private void registerWeatherMcpFunction() {
        // 天气查询函数参数定义
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        Map<String, Object> cityProp = new HashMap<>();
        cityProp.put("type", "string");
        cityProp.put("description", "城市名称，如 'Beijing' 或 'Shanghai'");
        properties.put("city", cityProp);
        
        Map<String, Object> unitsProp = new HashMap<>();
        unitsProp.put("type", "string");
        unitsProp.put("description", "温度单位，metric(摄氏度) 或 imperial(华氏度)");
        unitsProp.put("default", "metric");
        properties.put("units", unitsProp);
        
        schema.put("properties", properties);
        schema.put("required", List.of("city"));
        
        // 创建MCP函数适配器，传递ApplicationContext以支持城市名称映射
        McpFunctionAdapter adapter = new McpFunctionAdapter(
            WEATHER_MCP_SERVER_URL,
            "get_weather",
            mcpClient,
            applicationContext
        );
        
        functionRegistry.registerFunction(adapter);  // 修改这里：使用 adapter 而不是 weatherFunction
        logger.info("已注册天气查询MCP函数适配器");
    }
}