package com.example.aiagent.mcp.controller;

import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.model.McpRequest;
import com.example.aiagent.mcp.model.McpResponse;
import com.example.aiagent.mcp.registry.DynamicMcpServiceRegistry;
import com.example.aiagent.model.ToolDefinition;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 动态MCP控制器
 * 处理基于动态服务注册器的MCP请求
 */
@RestController
@RequestMapping("/api/mcp")
public class DynamicMcpController {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicMcpController.class);
    
    @Autowired
    private DynamicMcpServiceRegistry serviceRegistry;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 处理MCP请求
     */
    @PostMapping
    public McpResponse handleMcpRequest(@RequestBody McpRequest request,
                                       @RequestHeader(value = "X-User-ID", required = false) String userId,
                                       @RequestHeader(value = "X-User-Role", required = false) String userRole,
                                       @RequestHeader(value = "X-Session-ID", required = false) String sessionId) {
        
        logger.info("📨 收到MCP请求: {} (用户: {})", request.getMethod(), userId);
        
        McpResponse response = new McpResponse();
        response.setJsonrpc("2.0");
        response.setId(request.getId());
        
        try {
            // 创建执行上下文
            McpExecutionContext context = createExecutionContext(userId, userRole, sessionId, request);
            
            switch (request.getMethod()) {
                case "initialize":
                    logger.info("处理 initialize 请求");
                    Map<String, Object> initResult = handleInitialize(request, context);
                    response.setResult(initResult);
                    break;
                    
                case "tools/list":
                    logger.info("处理 tools/list 请求");
                    Map<String, Object> toolsList = handleToolsList(context);
                    response.setResult(toolsList);
                    break;
                    
                case "tools/call":
                    logger.info("处理 tools/call 请求");
                    Map<String, Object> result = handleToolCall(request, context);
                    response.setResult(result);
                    break;
                    
                case "notifications/initialized":
                    logger.info("处理 initialized 通知");
                    response.setResult(null);
                    break;
                    
                case "ping":
                    logger.info("处理 ping 请求");
                    response.setResult(Map.of("status", "pong"));
                    break;
                    
                default:
                    logger.warn("未知的MCP方法: {}", request.getMethod());
                    McpResponse.McpError error = new McpResponse.McpError();
                    error.setCode(-32601);
                    error.setMessage("Method not found: " + request.getMethod());
                    response.setError(error);
            }
            
        } catch (Exception e) {
            logger.error("❌ MCP请求处理失败: {}", e.getMessage(), e);
            McpResponse.McpError error = new McpResponse.McpError();
            error.setCode(-32603);
            error.setMessage("Internal error: " + e.getMessage());
            response.setError(error);
        }
        
        return response;
    }
    
    /**
     * 创建执行上下文
     */
    private McpExecutionContext createExecutionContext(String userId, String userRole, String sessionId, McpRequest request) {
        McpExecutionContext context = new McpExecutionContext();
        context.setUserId(userId != null ? userId : "anonymous");
        context.setRole(userRole != null ? userRole : "guest");
        context.setSessionId(sessionId != null ? sessionId : "session_" + System.currentTimeMillis());
        context.setRequestId(request.getId());
        
        // 设置默认权限
        Set<String> permissions = new HashSet<>();
        if ("admin".equals(userRole)) {
            permissions.addAll(Arrays.asList("user:read", "user:write", "weather:read", "admin"));
        } else if ("user".equals(userRole)) {
            permissions.addAll(Arrays.asList("weather:read"));
        }
        // guest用户只有基本权限
        
        context.setPermissions(permissions);
        
        return context;
    }
    
    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize(McpRequest request, McpExecutionContext context) {
        logger.info("处理MCP初始化请求 (用户: {})", context.getUserId());
        
        Map<String, Object> capabilities = new HashMap<>();
        
        // 服务器能力
        Map<String, Object> tools = new HashMap<>();
        tools.put("listChanged", false);
        capabilities.put("tools", tools);
        
        // 服务器信息
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "dynamic-mcp-server");
        serverInfo.put("version", "2.0.0");
        serverInfo.put("description", "Dynamic MCP Server with Spring Service Integration");
        
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("capabilities", capabilities);
        result.put("serverInfo", serverInfo);
        
        return result;
    }
    
    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList(McpExecutionContext context) {
        logger.info("获取工具列表 (用户: {}, 权限: {})", context.getUserId(), context.getPermissions());
        
        // 根据用户权限获取可用工具
        List<ToolDefinition> userTools = serviceRegistry.getToolDefinitionsForUser(context);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tools", userTools);
        
        logger.info("✅ 返回 {} 个可用工具", userTools.size());
        return result;
    }
    
    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolCall(McpRequest request, McpExecutionContext context) {
        Map<String, Object> params = request.getParams();
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        
        logger.info("🔧 调用工具: {} (用户: {}, 参数: {})", toolName, context.getUserId(), arguments);
        
        try {
            // 执行工具
            Object result = serviceRegistry.executeTool(toolName, arguments, context);
            
            // 构建MCP响应格式
            Map<String, Object> response = new HashMap<>();
            response.put("content", List.of(Map.of(
                "type", "text",
                "text", result
            )));
            response.put("isError", false);
            
            logger.info("✅ 工具调用成功: {}", toolName);
            return response;
            
        } catch (Exception e) {
            logger.error("❌ 工具调用失败: {} - {}", toolName, e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("content", List.of(Map.of(
                "type", "text",
                "text", "工具调用失败: " + e.getMessage()
            )));
            errorResponse.put("isError", true);
            
            return errorResponse;
        }
    }
    
    /**
     * 获取服务统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats(@RequestHeader(value = "X-User-ID", required = false) String userId) {
        logger.info("📊 获取MCP服务统计 (用户: {})", userId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalTools", serviceRegistry.getAllToolDefinitions().size());
        stats.put("timestamp", System.currentTimeMillis());
        stats.put("serverVersion", "2.0.0");
        
        // 按服务分组统计
        Map<String, Integer> serviceStats = new HashMap<>();
        for (ToolDefinition tool : serviceRegistry.getAllToolDefinitions()) {
            String serviceName = tool.getFunction().getName().split("_")[0];
            serviceStats.put(serviceName, serviceStats.getOrDefault(serviceName, 0) + 1);
        }
        stats.put("serviceBreakdown", serviceStats);
        
        return stats;
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        health.put("timestamp", System.currentTimeMillis());
        health.put("availableTools", serviceRegistry.getAllToolDefinitions().size());
        
        return health;
    }
}
