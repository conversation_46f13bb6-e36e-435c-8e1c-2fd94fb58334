package com.example.aiagent.mcp.services;

import com.example.aiagent.mcp.annotation.McpService;
import com.example.aiagent.mcp.annotation.McpTool;
import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.service.McpServiceInterface;
import com.example.aiagent.service.CityNameMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;

/**
 * 天气服务
 * 使用新的MCP架构提供天气查询功能
 */
@Service
@McpService(
    name = "weather",
    description = "天气查询服务，支持中文城市名查询",
    version = "2.0.0",
    requireAuth = false,
    permissions = {"weather:read"}
)
public class WeatherService implements McpServiceInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherService.class);
    
    @Autowired
    private CityNameMappingService cityNameMappingService;
    
    private final WebClient webClient = WebClient.builder().build();
    private long queryCount = 0;
    
    @Override
    public void initialize() {
        logger.info("🌤️ 初始化天气服务");
    }
    
    @Override
    public boolean hasPermission(McpExecutionContext context) {
        // 天气查询对所有用户开放，但记录用户信息
        return true;
    }
    
    @McpTool(
        name = "get_weather",
        description = "获取指定城市的当前天气信息，支持中文城市名",
        requireAuth = false,
        example = "get_weather('北京') 或 get_weather('Beijing')"
    )
    public Map<String, Object> getWeather(String city, McpExecutionContext context) {
        logger.info("🌤️ 查询天气: {} (用户: {})", city, context.getUserId());
        
        try {
            // 城市名称映射
            String mappedCity = cityNameMappingService.mapCityName(city);
            if (!city.equals(mappedCity)) {
                logger.info("🗺️ 城市名称映射: {} -> {}", city, mappedCity);
            }
            
            // 调用天气API (这里使用模拟数据)
            Map<String, Object> weatherData = fetchWeatherData(mappedCity);
            
            // 添加查询信息
            weatherData.put("originalCity", city);
            weatherData.put("mappedCity", mappedCity);
            weatherData.put("queryTime", System.currentTimeMillis());
            weatherData.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气查询成功: {}", city);
            return weatherData;
            
        } catch (Exception e) {
            logger.error("❌ 天气查询失败: {} - {}", city, e.getMessage(), e);
            throw new RuntimeException("天气查询失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "get_weather_forecast",
        description = "获取指定城市的天气预报（未来3天）",
        requireAuth = false,
        permissions = {"weather:read"},
        example = "get_weather_forecast('上海')"
    )
    public Map<String, Object> getWeatherForecast(String city, McpExecutionContext context) {
        logger.info("📅 查询天气预报: {} (用户: {})", city, context.getUserId());
        
        try {
            String mappedCity = cityNameMappingService.mapCityName(city);
            
            // 模拟天气预报数据
            Map<String, Object> forecast = new HashMap<>();
            forecast.put("city", mappedCity);
            forecast.put("originalCity", city);
            forecast.put("country", "CN");
            
            // 生成3天预报
            Map<String, Object>[] days = new Map[3];
            for (int i = 0; i < 3; i++) {
                Map<String, Object> dayForecast = new HashMap<>();
                dayForecast.put("date", "2024-08-" + String.format("%02d", i + 1));
                dayForecast.put("temperature", 25 + (Math.random() * 10));
                dayForecast.put("humidity", 60 + (Math.random() * 20));
                dayForecast.put("description", i == 0 ? "sunny" : (i == 1 ? "cloudy" : "rainy"));
                days[i] = dayForecast;
            }
            
            forecast.put("forecast", days);
            forecast.put("queryTime", System.currentTimeMillis());
            forecast.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气预报查询成功: {}", city);
            return forecast;
            
        } catch (Exception e) {
            logger.error("❌ 天气预报查询失败: {} - {}", city, e.getMessage(), e);
            throw new RuntimeException("天气预报查询失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "compare_weather",
        description = "比较两个城市的天气情况",
        requireAuth = false,
        example = "compare_weather('北京', '上海')"
    )
    public Map<String, Object> compareWeather(String city1, String city2, McpExecutionContext context) {
        logger.info("⚖️ 比较天气: {} vs {} (用户: {})", city1, city2, context.getUserId());
        
        try {
            Map<String, Object> weather1 = getWeatherInternal(city1);
            Map<String, Object> weather2 = getWeatherInternal(city2);
            
            Map<String, Object> comparison = new HashMap<>();
            comparison.put("city1", weather1);
            comparison.put("city2", weather2);
            
            // 添加比较结果
            double temp1 = (Double) weather1.get("temperature");
            double temp2 = (Double) weather2.get("temperature");
            
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("temperatureDifference", Math.abs(temp1 - temp2));
            analysis.put("warmerCity", temp1 > temp2 ? city1 : city2);
            analysis.put("recommendation", temp1 > temp2 ? 
                city1 + "比较温暖" : city2 + "比较温暖");
            
            comparison.put("analysis", analysis);
            comparison.put("queryTime", System.currentTimeMillis());
            comparison.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气比较完成: {} vs {}", city1, city2);
            return comparison;
            
        } catch (Exception e) {
            logger.error("❌ 天气比较失败: {} vs {} - {}", city1, city2, e.getMessage(), e);
            throw new RuntimeException("天气比较失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "get_weather_stats",
        description = "获取天气服务统计信息",
        requireAuth = false
    )
    public Map<String, Object> getWeatherStats(McpExecutionContext context) {
        logger.info("📊 获取天气服务统计 (用户: {})", context.getUserId());
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalQueries", queryCount);
        stats.put("serviceStatus", "active");
        stats.put("supportedCities", cityNameMappingService.getSupportedChineseCities().size());
        stats.put("timestamp", System.currentTimeMillis());
        
        return stats;
    }
    
    /**
     * 内部方法：获取天气数据
     */
    private Map<String, Object> getWeatherInternal(String city) {
        String mappedCity = cityNameMappingService.mapCityName(city);
        return fetchWeatherData(mappedCity);
    }
    
    /**
     * 模拟获取天气数据
     */
    private Map<String, Object> fetchWeatherData(String city) {
        // 这里应该调用真实的天气API，现在使用模拟数据
        Map<String, Object> weatherData = new HashMap<>();
        weatherData.put("city", city);
        weatherData.put("country", "CN");
        weatherData.put("temperature", 20 + (Math.random() * 20)); // 20-40度
        weatherData.put("humidity", 40 + (Math.random() * 40)); // 40-80%
        weatherData.put("pressure", 1000 + (Math.random() * 50)); // 1000-1050 hPa
        weatherData.put("windSpeed", Math.random() * 10); // 0-10 m/s
        weatherData.put("visibility", 5000 + (Math.random() * 5000)); // 5-10km
        weatherData.put("description", Math.random() > 0.5 ? "sunny" : "cloudy");
        weatherData.put("feelsLike", (Double) weatherData.get("temperature") + (Math.random() * 5 - 2.5));
        weatherData.put("units", "metric");
        
        return weatherData;
    }
    
    @Override
    public String getHealthStatus() {
        return String.format("healthy - processed %d weather queries", queryCount);
    }
}
