package com.example.aiagent.mcp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * MCP工具注解
 * 用于标记可以作为MCP工具暴露的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface McpTool {
    
    /**
     * 工具名称，如果不指定则使用方法名
     */
    String name() default "";
    
    /**
     * 工具描述
     */
    String description();
    
    /**
     * 是否需要用户认证
     */
    boolean requireAuth() default true;
    
    /**
     * 所需权限列表
     */
    String[] permissions() default {};
    
    /**
     * 参数示例（用于生成文档）
     */
    String example() default "";
}
