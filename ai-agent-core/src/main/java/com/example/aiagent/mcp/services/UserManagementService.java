package com.example.aiagent.mcp.services;

import com.example.aiagent.mcp.annotation.McpService;
import com.example.aiagent.mcp.annotation.McpTool;
import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.service.McpServiceInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户管理服务示例
 * 演示如何创建带权限控制的MCP服务
 */
@Service
@McpService(
    name = "user_management",
    description = "用户管理服务，提供用户信息查询和管理功能",
    version = "1.0.0",
    requireAuth = true,
    permissions = {"user:read", "user:write"}
)
public class UserManagementService implements McpServiceInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(UserManagementService.class);
    
    // 模拟用户数据
    private final Map<String, Map<String, Object>> users = new HashMap<>();
    
    @Override
    public void initialize() {
        logger.info("🚀 初始化用户管理服务");
        
        // 初始化一些测试用户
        initializeTestUsers();
    }
    
    private void initializeTestUsers() {
        Map<String, Object> user1 = new HashMap<>();
        user1.put("id", "user001");
        user1.put("name", "张三");
        user1.put("email", "<EMAIL>");
        user1.put("role", "admin");
        user1.put("permissions", Arrays.asList("user:read", "user:write", "weather:read"));
        users.put("user001", user1);
        
        Map<String, Object> user2 = new HashMap<>();
        user2.put("id", "user002");
        user2.put("name", "李四");
        user2.put("email", "<EMAIL>");
        user2.put("role", "user");
        user2.put("permissions", Arrays.asList("weather:read"));
        users.put("user002", user2);
        
        logger.info("✅ 初始化了 {} 个测试用户", users.size());
    }
    
    @Override
    public boolean hasPermission(McpExecutionContext context) {
        // 只有管理员或者查询自己的信息才允许
        return "admin".equals(context.getRole()) || 
               context.hasPermission("user:read");
    }
    
    @McpTool(
        name = "get_user_info",
        description = "获取用户信息",
        requireAuth = true,
        permissions = {"user:read"}
    )
    public Map<String, Object> getUserInfo(String userId, McpExecutionContext context) {
        logger.info("📋 获取用户信息: {} (请求者: {})", userId, context.getUserId());
        
        // 权限检查：只能查询自己的信息，除非是管理员
        if (!userId.equals(context.getUserId()) && !"admin".equals(context.getRole())) {
            throw new RuntimeException("权限不足：只能查询自己的用户信息");
        }
        
        Map<String, Object> user = users.get(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        // 返回用户信息（移除敏感信息）
        Map<String, Object> result = new HashMap<>(user);
        result.remove("permissions"); // 不返回权限信息给普通用户
        
        return result;
    }
    
    @McpTool(
        name = "list_users",
        description = "列出所有用户（仅管理员）",
        requireAuth = true,
        permissions = {"user:read", "admin"}
    )
    public List<Map<String, Object>> listUsers(McpExecutionContext context) {
        logger.info("📋 列出所有用户 (请求者: {})", context.getUserId());
        
        // 只有管理员才能列出所有用户
        if (!"admin".equals(context.getRole())) {
            throw new RuntimeException("权限不足：只有管理员才能列出所有用户");
        }
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> user : users.values()) {
            Map<String, Object> userInfo = new HashMap<>(user);
            userInfo.remove("permissions"); // 移除敏感信息
            result.add(userInfo);
        }
        
        return result;
    }
    
    @McpTool(
        name = "update_user_profile",
        description = "更新用户资料",
        requireAuth = true,
        permissions = {"user:write"}
    )
    public Map<String, Object> updateUserProfile(String userId, String name, String email, McpExecutionContext context) {
        logger.info("✏️ 更新用户资料: {} (请求者: {})", userId, context.getUserId());
        
        // 权限检查：只能更新自己的资料，除非是管理员
        if (!userId.equals(context.getUserId()) && !"admin".equals(context.getRole())) {
            throw new RuntimeException("权限不足：只能更新自己的用户资料");
        }
        
        Map<String, Object> user = users.get(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        // 更新用户信息
        if (name != null && !name.trim().isEmpty()) {
            user.put("name", name.trim());
        }
        if (email != null && !email.trim().isEmpty()) {
            user.put("email", email.trim());
        }
        
        user.put("updatedAt", new Date());
        user.put("updatedBy", context.getUserId());
        
        logger.info("✅ 用户资料更新成功: {}", userId);
        
        // 返回更新后的用户信息
        Map<String, Object> result = new HashMap<>(user);
        result.remove("permissions");
        return result;
    }
    
    @McpTool(
        name = "get_my_permissions",
        description = "获取当前用户的权限列表",
        requireAuth = true
    )
    public Map<String, Object> getMyPermissions(McpExecutionContext context) {
        logger.info("🔐 获取用户权限: {}", context.getUserId());
        
        Map<String, Object> result = new HashMap<>();
        result.put("userId", context.getUserId());
        result.put("role", context.getRole());
        result.put("permissions", context.getPermissions());
        result.put("timestamp", new Date());
        
        return result;
    }
    
    @Override
    public String getHealthStatus() {
        return String.format("healthy - managing %d users", users.size());
    }
}
