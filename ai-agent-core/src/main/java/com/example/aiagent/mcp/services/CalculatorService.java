package com.example.aiagent.mcp.services;

import com.example.aiagent.mcp.annotation.McpService;
import com.example.aiagent.mcp.annotation.McpTool;
import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.service.McpServiceInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 计算器服务示例
 * 演示如何创建不需要特殊权限的MCP服务
 */
@Service
@McpService(
    name = "calculator",
    description = "数学计算服务，提供基本的数学运算功能",
    version = "1.0.0",
    requireAuth = false  // 不需要认证
)
public class CalculatorService implements McpServiceInterface {
    
    private static final Logger logger = LoggerFactory.getLogger(CalculatorService.class);
    
    private long calculationCount = 0;
    
    @Override
    public void initialize() {
        logger.info("🧮 初始化计算器服务");
    }
    
    @McpTool(
        name = "add",
        description = "加法运算",
        requireAuth = false,
        example = "add(5, 3) = 8"
    )
    public Map<String, Object> add(double a, double b, McpExecutionContext context) {
        logger.info("➕ 执行加法: {} + {} (用户: {})", a, b, context.getUserId());
        
        double result = a + b;
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "addition");
        response.put("operands", new double[]{a, b});
        response.put("result", result);
        response.put("formula", a + " + " + b + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "subtract",
        description = "减法运算",
        requireAuth = false,
        example = "subtract(10, 3) = 7"
    )
    public Map<String, Object> subtract(double a, double b, McpExecutionContext context) {
        logger.info("➖ 执行减法: {} - {} (用户: {})", a, b, context.getUserId());
        
        double result = a - b;
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "subtraction");
        response.put("operands", new double[]{a, b});
        response.put("result", result);
        response.put("formula", a + " - " + b + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "multiply",
        description = "乘法运算",
        requireAuth = false,
        example = "multiply(4, 5) = 20"
    )
    public Map<String, Object> multiply(double a, double b, McpExecutionContext context) {
        logger.info("✖️ 执行乘法: {} × {} (用户: {})", a, b, context.getUserId());
        
        double result = a * b;
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "multiplication");
        response.put("operands", new double[]{a, b});
        response.put("result", result);
        response.put("formula", a + " × " + b + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "divide",
        description = "除法运算",
        requireAuth = false,
        example = "divide(15, 3) = 5"
    )
    public Map<String, Object> divide(double a, double b, McpExecutionContext context) {
        logger.info("➗ 执行除法: {} ÷ {} (用户: {})", a, b, context.getUserId());
        
        if (b == 0) {
            throw new RuntimeException("除数不能为零");
        }
        
        double result = a / b;
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "division");
        response.put("operands", new double[]{a, b});
        response.put("result", result);
        response.put("formula", a + " ÷ " + b + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "power",
        description = "幂运算",
        requireAuth = false,
        example = "power(2, 3) = 8"
    )
    public Map<String, Object> power(double base, double exponent, McpExecutionContext context) {
        logger.info("🔢 执行幂运算: {} ^ {} (用户: {})", base, exponent, context.getUserId());
        
        double result = Math.pow(base, exponent);
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "power");
        response.put("operands", new double[]{base, exponent});
        response.put("result", result);
        response.put("formula", base + " ^ " + exponent + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "sqrt",
        description = "平方根运算",
        requireAuth = false,
        example = "sqrt(16) = 4"
    )
    public Map<String, Object> sqrt(double number, McpExecutionContext context) {
        logger.info("√ 执行平方根: √{} (用户: {})", number, context.getUserId());
        
        if (number < 0) {
            throw new RuntimeException("不能计算负数的平方根");
        }
        
        double result = Math.sqrt(number);
        calculationCount++;
        
        Map<String, Object> response = new HashMap<>();
        response.put("operation", "square_root");
        response.put("operand", number);
        response.put("result", result);
        response.put("formula", "√" + number + " = " + result);
        response.put("timestamp", System.currentTimeMillis());
        
        return response;
    }
    
    @McpTool(
        name = "get_calculation_stats",
        description = "获取计算统计信息",
        requireAuth = false
    )
    public Map<String, Object> getCalculationStats(McpExecutionContext context) {
        logger.info("📊 获取计算统计 (用户: {})", context.getUserId());
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCalculations", calculationCount);
        stats.put("serviceStatus", "active");
        stats.put("timestamp", System.currentTimeMillis());
        
        return stats;
    }
    
    @Override
    public String getHealthStatus() {
        return String.format("healthy - performed %d calculations", calculationCount);
    }
}
