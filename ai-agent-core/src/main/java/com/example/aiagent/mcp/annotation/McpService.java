package com.example.aiagent.mcp.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * MCP服务注解
 * 用于标记可以作为MCP工具发布的Spring Service
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface McpService {
    
    /**
     * 服务名称，如果不指定则使用类名
     */
    String name() default "";
    
    /**
     * 服务描述
     */
    String description() default "";
    
    /**
     * 服务版本
     */
    String version() default "1.0.0";
    
    /**
     * 是否需要用户认证
     */
    boolean requireAuth() default true;
    
    /**
     * 所需权限列表
     */
    String[] permissions() default {};
}
