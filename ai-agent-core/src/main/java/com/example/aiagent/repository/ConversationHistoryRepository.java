package com.example.aiagent.repository;

import com.example.aiagent.entity.ConversationHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话历史记录Repository
 */
@Repository
public interface ConversationHistoryRepository extends JpaRepository<ConversationHistory, Long> {
    
    /**
     * 根据会话ID查询对话历史，按时间排序
     */
    List<ConversationHistory> findBySessionIdOrderByCreatedAtAsc(String sessionId);
    
    /**
     * 根据用户ID查询对话历史，分页
     */
    Page<ConversationHistory> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);
    
    /**
     * 根据用户ID和时间范围查询对话历史
     */
    List<ConversationHistory> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
            String userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据会话ID和消息类型查询
     */
    List<ConversationHistory> findBySessionIdAndMessageTypeOrderByCreatedAtAsc(
            String sessionId, ConversationHistory.MessageType messageType);
    
    /**
     * 查询用户的最近N条对话记录
     */
    @Query("SELECT ch FROM ConversationHistory ch WHERE ch.userId = :userId " +
           "ORDER BY ch.createdAt DESC")
    List<ConversationHistory> findRecentConversations(@Param("userId") String userId, Pageable pageable);
    
    /**
     * 查询指定会话的消息数量
     */
    long countBySessionId(String sessionId);
    
    /**
     * 查询用户在指定时间范围内的对话数量
     */
    @Query("SELECT COUNT(ch) FROM ConversationHistory ch WHERE ch.userId = :userId " +
           "AND ch.createdAt BETWEEN :startTime AND :endTime")
    long countUserConversationsInTimeRange(@Param("userId") String userId, 
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询所有会话ID（用于统计）
     */
    @Query("SELECT DISTINCT ch.sessionId FROM ConversationHistory ch WHERE ch.userId = :userId " +
           "ORDER BY MAX(ch.createdAt) DESC")
    List<String> findDistinctSessionIdsByUserId(@Param("userId") String userId);
    
    /**
     * 删除指定时间之前的历史记录
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);
    
    /**
     * 查询包含错误的对话记录
     */
    List<ConversationHistory> findByErrorMessageIsNotNullOrderByCreatedAtDesc();
    
    /**
     * 根据认知架构类型查询统计
     */
    @Query("SELECT ch.cognitiveArchitecture, COUNT(ch) FROM ConversationHistory ch " +
           "WHERE ch.cognitiveArchitecture IS NOT NULL " +
           "GROUP BY ch.cognitiveArchitecture")
    List<Object[]> countByCognitiveArchitecture();
}
