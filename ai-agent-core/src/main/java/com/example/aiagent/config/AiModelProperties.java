package com.example.aiagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "ai")
public class AiModelProperties {
    
    private ModelConfig model = new ModelConfig();
    private McpConfig mcp = new McpConfig();
    private KnowledgeConfig knowledge = new KnowledgeConfig();
    
    @Data
    public static class ModelConfig {
        private AlibabaConfig alibaba = new AlibabaConfig();
    }
    
    @Data
    public static class AlibabaConfig {
        private String apiKey;
        private String endpoint;
        private String modelName;
        private Integer maxTokens = 2000;
        private Double temperature = 0.7;
    }
    
    @Data
    public static class McpConfig {
        private boolean enabled = false;
        private List<ServerConfig> servers;
    }
    
    @Data
    public static class ServerConfig {
        private String name;
        private String command;
        private List<String> args;
        private Map<String, String> env;
    }
    
    @Data
    public static class KnowledgeConfig {
        private boolean enabled = false;
        private VectorStoreConfig vectorStore = new VectorStoreConfig();
        private RetrievalConfig retrieval = new RetrievalConfig();
    }
    
    @Data
    public static class VectorStoreConfig {
        private String type = "memory";
        private Integer dimension = 1536;
    }
    
    @Data
    public static class RetrievalConfig {
        private Integer topK = 5;
        private Double similarityThreshold = 0.7;
    }
}