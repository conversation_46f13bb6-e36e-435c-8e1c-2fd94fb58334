package com.example.aiagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI模型配置类
 * 简化版配置，用于服务层
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai.model")
public class AiModelConfig {
    
    private String provider;
    private String apiKey;
    private String endpoint;
    private String modelName;
    private Integer maxTokens = 2000;
    private Double temperature = 0.7;
    private Integer timeoutSeconds = 30;
    
    /**
     * 从AiModelProperties创建AiModelConfig
     */
    public static AiModelConfig fromProperties(AiModelProperties properties) {
        AiModelConfig config = new AiModelConfig();
        AiModelProperties.AlibabaConfig alibaba = properties.getModel().getAlibaba();
        
        config.setProvider("alibaba");
        config.setApiKey(alibaba.getApiKey());
        config.setEndpoint(alibaba.getEndpoint());
        config.setModelName(alibaba.getModelName());
        config.setMaxTokens(alibaba.getMaxTokens());
        config.setTemperature(alibaba.getTemperature());
        
        return config;
    }
}