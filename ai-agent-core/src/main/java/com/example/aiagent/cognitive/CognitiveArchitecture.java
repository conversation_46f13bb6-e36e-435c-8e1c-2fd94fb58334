package com.example.aiagent.cognitive;

import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 认知架构接口
 * 定义AI Agent的认知处理流程：感知 → 规划 → 行动 → 反思
 */
public interface CognitiveArchitecture {
    
    /**
     * 同步认知处理
     */
    AgentResponse process(AgentRequest request);
    
    /**
     * 流式认知处理
     */
    void processStream(AgentRequest request, SseEmitter emitter);
    
    /**
     * 获取架构名称
     */
    String getArchitectureName();
    
    /**
     * 检查是否支持指定的请求
     */
    boolean supports(AgentRequest request);
}
