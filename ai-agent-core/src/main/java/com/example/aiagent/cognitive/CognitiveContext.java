package com.example.aiagent.cognitive;

import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.ChatMessage;
import com.example.aiagent.model.ToolDefinition;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认知上下文
 * 在整个认知处理过程中传递和维护状态信息
 */
public class CognitiveContext {
    
    // 基本信息
    private final AgentRequest originalRequest;
    private final List<ChatMessage> conversation;
    private final Map<String, Object> metadata;
    
    // 感知结果
    private String userIntent;
    private List<String> extractedEntities;
    private Map<String, Object> contextualInfo;
    
    // 规划结果
    private List<String> plannedActions;
    private List<ToolDefinition> requiredTools;
    private String executionStrategy;
    
    // 行动结果
    private List<String> executedActions;
    private Map<String, Object> actionResults;
    private boolean allActionsCompleted;
    
    // 反思结果
    private String finalAnswer;
    private List<String> lessonsLearned;
    private double confidenceScore;
    
    // 流式输出
    private SseEmitter emitter;
    private boolean isStreamMode;
    
    public CognitiveContext(AgentRequest request) {
        this.originalRequest = request;
        this.conversation = new ArrayList<>();
        this.metadata = new HashMap<>();
        this.extractedEntities = new ArrayList<>();
        this.contextualInfo = new HashMap<>();
        this.plannedActions = new ArrayList<>();
        this.requiredTools = new ArrayList<>();
        this.executedActions = new ArrayList<>();
        this.actionResults = new HashMap<>();
        this.lessonsLearned = new ArrayList<>();
        this.allActionsCompleted = false;
        this.confidenceScore = 0.0;
        this.isStreamMode = false;
    }
    
    // ==================== 流式输出方法 ====================
    
    public void enableStreamMode(SseEmitter emitter) {
        this.emitter = emitter;
        this.isStreamMode = true;
    }
    
    public void streamOutput(String message) {
        if (isStreamMode && emitter != null) {
            try {
                emitter.send(message);
            } catch (Exception e) {
                // 忽略流式输出错误
            }
        }
    }
    
    public void streamPhaseStart(String phase) {
        streamOutput(String.format("### 🧠 %s 阶段\n\n", phase));
    }
    
    public void streamThought(String thought) {
        streamOutput(String.format("💭 **思考**: %s\n\n", thought));
    }
    
    public void streamAction(String action) {
        streamOutput(String.format("⚡ **行动**: %s\n\n", action));
    }
    
    public void streamResult(String result) {
        streamOutput(String.format("📊 **结果**: %s\n\n", result));
    }
    
    // ==================== Getter/Setter 方法 ====================
    
    public AgentRequest getOriginalRequest() { return originalRequest; }
    public List<ChatMessage> getConversation() { return conversation; }
    public Map<String, Object> getMetadata() { return metadata; }
    
    public String getUserIntent() { return userIntent; }
    public void setUserIntent(String userIntent) { this.userIntent = userIntent; }
    
    public List<String> getExtractedEntities() { return extractedEntities; }
    public void setExtractedEntities(List<String> extractedEntities) { this.extractedEntities = extractedEntities; }
    
    public Map<String, Object> getContextualInfo() { return contextualInfo; }
    public void setContextualInfo(Map<String, Object> contextualInfo) { this.contextualInfo = contextualInfo; }
    
    public List<String> getPlannedActions() { return plannedActions; }
    public void setPlannedActions(List<String> plannedActions) { this.plannedActions = plannedActions; }
    
    public List<ToolDefinition> getRequiredTools() { return requiredTools; }
    public void setRequiredTools(List<ToolDefinition> requiredTools) { this.requiredTools = requiredTools; }
    
    public String getExecutionStrategy() { return executionStrategy; }
    public void setExecutionStrategy(String executionStrategy) { this.executionStrategy = executionStrategy; }
    
    public List<String> getExecutedActions() { return executedActions; }
    public void setExecutedActions(List<String> executedActions) { this.executedActions = executedActions; }
    
    public Map<String, Object> getActionResults() { return actionResults; }
    public void setActionResults(Map<String, Object> actionResults) { this.actionResults = actionResults; }
    
    public boolean isAllActionsCompleted() { return allActionsCompleted; }
    public void setAllActionsCompleted(boolean allActionsCompleted) { this.allActionsCompleted = allActionsCompleted; }
    
    public String getFinalAnswer() { return finalAnswer; }
    public void setFinalAnswer(String finalAnswer) { this.finalAnswer = finalAnswer; }
    
    public List<String> getLessonsLearned() { return lessonsLearned; }
    public void setLessonsLearned(List<String> lessonsLearned) { this.lessonsLearned = lessonsLearned; }
    
    public double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
    
    public boolean isStreamMode() { return isStreamMode; }
    public SseEmitter getEmitter() { return emitter; }
}
