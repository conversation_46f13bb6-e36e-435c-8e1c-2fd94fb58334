package com.example.aiagent.cognitive.impl.components.fc;

import com.example.aiagent.cognitive.CognitiveContext;
import com.example.aiagent.cognitive.components.PerceptionComponent;
import com.example.aiagent.function.FunctionRegistry;
import com.example.aiagent.model.ToolDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Function Calling 感知组件
 * 快速识别用户意图和所需的函数调用
 */
@Component
public class FCPerceptionComponent implements PerceptionComponent {
    
    private static final Logger logger = LoggerFactory.getLogger(FCPerceptionComponent.class);
    
    @Autowired
    private FunctionRegistry functionRegistry;
    
    // 意图识别模式
    private static final Pattern WEATHER_PATTERN = Pattern.compile(".*(?:天气|气温|温度|下雨|晴天|多云).*", Pattern.CASE_INSENSITIVE);
    private static final Pattern CALCULATION_PATTERN = Pattern.compile(".*(?:计算|算|加|减|乘|除|\\+|\\-|\\*|/).*", Pattern.CASE_INSENSITIVE);
    private static final Pattern TIME_PATTERN = Pattern.compile(".*(?:时间|现在|几点|日期|今天|明天).*", Pattern.CASE_INSENSITIVE);
    
    @Override
    public void perceive(CognitiveContext context) {
        logger.debug("🔍 FC感知: 分析用户输入");
        
        String userMessage = context.getOriginalRequest().getMessage();
        
        // 1. 识别用户意图
        String intent = identifyIntent(userMessage);
        context.setUserIntent(intent);
        
        // 2. 提取实体信息
        List<String> entities = extractEntities(userMessage, intent);
        context.setExtractedEntities(entities);
        
        // 3. 设置上下文信息
        context.getContextualInfo().put("message_length", userMessage.length());
        context.getContextualInfo().put("contains_question", userMessage.contains("?") || userMessage.contains("？"));
        context.getContextualInfo().put("available_functions", functionRegistry.getAllFunctionNames());
        
        logger.debug("✅ FC感知完成: 意图={}, 实体={}", intent, entities);
    }
    
    /**
     * 识别用户意图
     */
    private String identifyIntent(String message) {
        if (WEATHER_PATTERN.matcher(message).matches()) {
            return "weather_query";
        } else if (CALCULATION_PATTERN.matcher(message).matches()) {
            return "calculation";
        } else if (TIME_PATTERN.matcher(message).matches()) {
            return "time_query";
        } else {
            return "general_query";
        }
    }
    
    /**
     * 提取实体信息
     */
    private List<String> extractEntities(String message, String intent) {
        List<String> entities = new ArrayList<>();
        
        switch (intent) {
            case "weather_query":
                // 提取城市名称
                entities.addAll(extractCityNames(message));
                break;
            case "calculation":
                // 提取数字和运算符
                entities.addAll(extractMathExpressions(message));
                break;
            case "time_query":
                // 提取时间相关词汇
                entities.addAll(extractTimeExpressions(message));
                break;
        }
        
        return entities;
    }
    
    /**
     * 提取城市名称
     */
    private List<String> extractCityNames(String message) {
        List<String> cities = new ArrayList<>();
        
        // 简单的城市名称匹配
        String[] commonCities = {"北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "西安", "天津", "重庆"};
        for (String city : commonCities) {
            if (message.contains(city)) {
                cities.add(city);
            }
        }
        
        // 英文城市名称
        String[] englishCities = {"Beijing", "Shanghai", "Guangzhou", "Shenzhen", "Tokyo", "New York", "London", "Paris"};
        for (String city : englishCities) {
            if (message.toLowerCase().contains(city.toLowerCase())) {
                cities.add(city);
            }
        }
        
        return cities;
    }
    
    /**
     * 提取数学表达式
     */
    private List<String> extractMathExpressions(String message) {
        List<String> expressions = new ArrayList<>();
        
        // 简单的数字提取
        Pattern numberPattern = Pattern.compile("\\d+(?:\\.\\d+)?");
        java.util.regex.Matcher matcher = numberPattern.matcher(message);
        while (matcher.find()) {
            expressions.add(matcher.group());
        }
        
        return expressions;
    }
    
    /**
     * 提取时间表达式
     */
    private List<String> extractTimeExpressions(String message) {
        List<String> timeExpressions = new ArrayList<>();
        
        String[] timeKeywords = {"现在", "今天", "明天", "昨天", "时间", "几点"};
        for (String keyword : timeKeywords) {
            if (message.contains(keyword)) {
                timeExpressions.add(keyword);
            }
        }
        
        return timeExpressions;
    }
    
    @Override
    public String getComponentName() {
        return "FCPerceptionComponent";
    }
}
