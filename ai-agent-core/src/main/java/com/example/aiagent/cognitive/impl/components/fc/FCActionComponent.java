package com.example.aiagent.cognitive.impl.components.fc;

import com.example.aiagent.cognitive.CognitiveContext;
import com.example.aiagent.cognitive.components.ActionComponent;
import com.example.aiagent.function.FunctionExecutor;
import com.example.aiagent.function.FunctionResult;
import com.example.aiagent.model.*;
import com.example.aiagent.service.AiModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Function Calling 行动组件
 * 执行AI模型的函数调用决策
 */
@Component
public class FCActionComponent implements ActionComponent {
    
    private static final Logger logger = LoggerFactory.getLogger(FCActionComponent.class);
    
    @Autowired
    private AiModelService aiModelService;
    
    @Autowired
    private FunctionExecutor functionExecutor;
    
    @Override
    public void act(CognitiveContext context) {
        logger.debug("⚡ FC行动: 执行函数调用");
        
        try {
            // 1. 让AI模型分析并决定函数调用
            List<ToolCall> toolCalls = analyzeAndDecideFunctionCalls(context);
            
            // 2. 执行函数调用
            Map<String, Object> results = executeFunctionCalls(context, toolCalls);
            
            // 3. 更新上下文
            updateContextWithResults(context, results);
            
            logger.debug("✅ FC行动完成: 执行了{}个函数调用", results.size());
            
        } catch (Exception e) {
            logger.error("❌ FC行动失败: {}", e.getMessage(), e);
            context.getActionResults().put("error", e.getMessage());
            context.setAllActionsCompleted(false);
        }
    }
    
    /**
     * 让AI模型分析并决定需要调用的函数
     */
    private List<ToolCall> analyzeAndDecideFunctionCalls(CognitiveContext context) {
        context.streamAction("让AI模型分析需要调用的函数...");
        
        // 构建聊天请求
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setMessages(context.getConversation());
        chatRequest.setTools(context.getRequiredTools());
        
        // 调用AI模型
        ChatResponse response = aiModelService.chat(chatRequest);
        ChatMessage assistantMessage = response.getChoices().get(0).getMessage();
        
        // 添加到对话历史
        context.getConversation().add(assistantMessage);
        
        // 返回工具调用
        return assistantMessage.getToolCalls() != null ? assistantMessage.getToolCalls() : new ArrayList<>();
    }
    
    /**
     * 执行函数调用
     */
    private Map<String, Object> executeFunctionCalls(CognitiveContext context, List<ToolCall> toolCalls) {
        Map<String, Object> results = new HashMap<>();
        List<String> executedActions = new ArrayList<>();
        
        if (toolCalls.isEmpty()) {
            context.streamAction("AI模型决定不需要调用函数");
            results.put("no_function_calls", true);
            return results;
        }
        
        context.streamAction(String.format("开始执行%d个函数调用", toolCalls.size()));
        
        for (ToolCall toolCall : toolCalls) {
            String functionName = toolCall.getFunction().getName();
            String arguments = toolCall.getFunction().getArguments();
            
            context.streamAction(String.format("调用函数: %s(%s)", functionName, arguments));
            
            try {
                // 执行函数
                FunctionResult result = functionExecutor.execute(toolCall);
                
                // 记录结果
                results.put(functionName, result);
                executedActions.add(functionName);
                
                // 添加工具消息到对话历史
                ChatMessage toolMessage = ChatMessage.tool(toolCall.getId(), result.toString());
                context.getConversation().add(toolMessage);
                
                context.streamAction(String.format("函数 %s 执行%s", functionName, result.isSuccess() ? "成功" : "失败"));
                
            } catch (Exception e) {
                logger.error("函数调用失败: {} - {}", functionName, e.getMessage());
                results.put(functionName + "_error", e.getMessage());
                context.streamAction(String.format("函数 %s 执行失败: %s", functionName, e.getMessage()));
            }
        }
        
        context.setExecutedActions(executedActions);
        return results;
    }
    
    /**
     * 更新上下文结果
     */
    private void updateContextWithResults(CognitiveContext context, Map<String, Object> results) {
        context.setActionResults(results);
        
        // 检查是否所有行动都完成
        boolean allCompleted = results.values().stream()
            .noneMatch(result -> result instanceof String && ((String) result).contains("error"));
        
        context.setAllActionsCompleted(allCompleted);
        
        // 记录执行统计
        context.getMetadata().put("functions_executed", context.getExecutedActions().size());
        context.getMetadata().put("execution_success", allCompleted);
    }
    
    @Override
    public String getComponentName() {
        return "FCActionComponent";
    }
}
