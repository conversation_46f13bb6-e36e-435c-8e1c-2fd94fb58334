package com.example.aiagent.cognitive;

import com.example.aiagent.cognitive.components.*;
import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import com.example.aiagent.model.ChatMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 基础认知架构实现
 * 实现标准的 Perception → Planning → Action → Reflection 流程
 */
public abstract class BaseCognitiveArchitecture implements CognitiveArchitecture {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseCognitiveArchitecture.class);
    
    protected final PerceptionComponent perceptionComponent;
    protected final PlanningComponent planningComponent;
    protected final ActionComponent actionComponent;
    protected final ReflectionComponent reflectionComponent;
    
    public BaseCognitiveArchitecture(
            PerceptionComponent perceptionComponent,
            PlanningComponent planningComponent,
            ActionComponent actionComponent,
            ReflectionComponent reflectionComponent) {
        this.perceptionComponent = perceptionComponent;
        this.planningComponent = planningComponent;
        this.actionComponent = actionComponent;
        this.reflectionComponent = reflectionComponent;
    }
    
    @Override
    public AgentResponse process(AgentRequest request) {
        logger.info("🧠 开始认知处理: {} (架构: {})", request.getMessage(), getArchitectureName());
        
        try {
            // 创建认知上下文
            CognitiveContext context = createContext(request);
            
            // 执行认知流程
            executeCognitiveFlow(context);
            
            // 构建响应
            return buildResponse(context);
            
        } catch (Exception e) {
            logger.error("❌ 认知处理失败: {}", e.getMessage(), e);
            return AgentResponse.error("认知处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public void processStream(AgentRequest request, SseEmitter emitter) {
        logger.info("🌊 开始流式认知处理: {} (架构: {})", request.getMessage(), getArchitectureName());
        
        try {
            // 创建认知上下文并启用流式模式
            CognitiveContext context = createContext(request);
            context.enableStreamMode(emitter);
            
            // 发送开始信息
            context.streamOutput("🧠 **开始认知处理**\n\n");
            context.streamOutput(String.format("**架构**: %s\n", getArchitectureName()));
            context.streamOutput(String.format("**用户问题**: %s\n\n", request.getMessage()));
            
            // 执行认知流程
            executeCognitiveFlow(context);
            
            // 发送完成信息
            context.streamOutput("\n✅ **认知处理完成！**\n");
            emitter.complete();
            
        } catch (Exception e) {
            logger.error("❌ 流式认知处理失败: {}", e.getMessage(), e);
            try {
                emitter.send("❌ 认知处理失败: " + e.getMessage());
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
        }
    }
    
    /**
     * 执行核心认知流程
     */
    protected void executeCognitiveFlow(CognitiveContext context) {
        // 1. 感知阶段 (Perception)
        executePerception(context);
        
        // 2. 规划阶段 (Planning)
        executePlanning(context);
        
        // 3. 行动阶段 (Action)
        executeAction(context);
        
        // 4. 反思阶段 (Reflection)
        executeReflection(context);
    }
    
    /**
     * 执行感知阶段
     */
    protected void executePerception(CognitiveContext context) {
        logger.debug("👁️ 执行感知阶段");
        context.streamPhaseStart("感知 (Perception)");
        context.streamThought("正在理解用户意图，提取关键信息...");
        
        perceptionComponent.perceive(context);
        
        context.streamResult(String.format("识别意图: %s", context.getUserIntent()));
        if (!context.getExtractedEntities().isEmpty()) {
            context.streamResult(String.format("提取实体: %s", context.getExtractedEntities()));
        }
    }
    
    /**
     * 执行规划阶段
     */
    protected void executePlanning(CognitiveContext context) {
        logger.debug("📋 执行规划阶段");
        context.streamPhaseStart("规划 (Planning)");
        context.streamThought("正在制定执行计划，选择合适的工具和策略...");
        
        planningComponent.plan(context);
        
        context.streamResult(String.format("执行策略: %s", context.getExecutionStrategy()));
        if (!context.getPlannedActions().isEmpty()) {
            context.streamResult(String.format("计划行动: %s", context.getPlannedActions()));
        }
    }
    
    /**
     * 执行行动阶段
     */
    protected void executeAction(CognitiveContext context) {
        logger.debug("⚡ 执行行动阶段");
        context.streamPhaseStart("行动 (Action)");
        context.streamThought("正在执行计划中的具体行动...");
        
        actionComponent.act(context);
        
        context.streamResult(String.format("执行行动: %s", context.getExecutedActions()));
        context.streamResult(String.format("行动完成: %s", context.isAllActionsCompleted() ? "是" : "否"));
    }
    
    /**
     * 执行反思阶段
     */
    protected void executeReflection(CognitiveContext context) {
        logger.debug("🤔 执行反思阶段");
        context.streamPhaseStart("反思 (Reflection)");
        context.streamThought("正在评估执行结果，生成最终答案...");
        
        reflectionComponent.reflect(context);
        
        context.streamResult(String.format("置信度: %.2f", context.getConfidenceScore()));
        context.streamOutput("### 📝 **最终答案**\n\n");
        context.streamOutput(context.getFinalAnswer());
    }
    
    /**
     * 创建认知上下文
     */
    protected CognitiveContext createContext(AgentRequest request) {
        CognitiveContext context = new CognitiveContext(request);
        
        // 初始化对话历史
        context.getConversation().add(ChatMessage.user(request.getMessage()));
        
        // 设置元数据
        context.getMetadata().put("architecture", getArchitectureName());
        context.getMetadata().put("mode", request.getMode());
        context.getMetadata().put("timestamp", System.currentTimeMillis());
        
        return context;
    }
    
    /**
     * 构建最终响应
     */
    protected AgentResponse buildResponse(CognitiveContext context) {
        AgentResponse response = new AgentResponse();
        
        // 设置基本信息
        response.setContent(context.getFinalAnswer());
        response.setConversation(context.getConversation());
        response.setSuccess(true);
        response.setSessionId(context.getOriginalRequest().getSessionId());
        response.setMode(context.getOriginalRequest().getMode());
        
        // 设置统计信息
        response.setFunctionsUsed(context.getExecutedActions());
        response.setIterations(calculateIterations(context));
        
        return response;
    }
    
    /**
     * 计算迭代次数
     */
    protected int calculateIterations(CognitiveContext context) {
        // 基于执行的行动数量计算迭代次数
        return Math.max(1, context.getExecutedActions().size());
    }
}
