package com.example.aiagent.cognitive.impl;

import com.example.aiagent.cognitive.BaseCognitiveArchitecture;
import com.example.aiagent.cognitive.CognitiveContext;
import com.example.aiagent.cognitive.impl.components.fc.*;
import com.example.aiagent.model.AgentMode;
import com.example.aiagent.model.AgentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ReAct 认知架构
 * 实现迭代式的推理-行动-观察循环
 */
@Component
public class ReActArchitecture extends BaseCognitiveArchitecture {
    
    private static final int MAX_ITERATIONS = 5;
    
    @Autowired
    public ReActArchitecture(
            FCPerceptionComponent perceptionComponent,
            FCPlanningComponent planningComponent,
            FCActionComponent actionComponent,
            FCReflectionComponent reflectionComponent) {
        super(perceptionComponent, planningComponent, actionComponent, reflectionComponent);
    }
    
    @Override
    public String getArchitectureName() {
        return "ReAct Architecture";
    }
    
    @Override
    public boolean supports(AgentRequest request) {
        return request.getMode() == AgentMode.REACT;
    }
    
    /**
     * ReAct特有的迭代式认知流程
     */
    @Override
    protected void executeCognitiveFlow(CognitiveContext context) {
        // 初始感知
        executePerception(context);
        
        // 迭代式的规划-行动-反思循环
        for (int iteration = 1; iteration <= MAX_ITERATIONS; iteration++) {
            context.streamOutput(String.format("### 🔄 第 %d 轮迭代\n\n", iteration));
            
            // 规划阶段
            executePlanning(context);
            
            // 行动阶段
            executeAction(context);
            
            // 检查是否需要继续迭代
            if (shouldStopIteration(context)) {
                context.streamOutput("🎯 **达成目标，停止迭代**\n\n");
                break;
            }
            
            // 中间反思（为下一轮迭代做准备）
            executeIntermediateReflection(context, iteration);
            
            context.streamOutput("---\n\n");
        }
        
        // 最终反思
        executeReflection(context);
    }
    
    /**
     * 判断是否应该停止迭代
     */
    private boolean shouldStopIteration(CognitiveContext context) {
        // 如果所有行动都完成且有明确答案，则停止
        return context.isAllActionsCompleted() && 
               context.getFinalAnswer() != null && 
               !context.getFinalAnswer().isEmpty();
    }
    
    /**
     * 执行中间反思（为下一轮迭代做准备）
     */
    private void executeIntermediateReflection(CognitiveContext context, int iteration) {
        context.streamPhaseStart("中间反思");
        context.streamThought(String.format("第%d轮迭代完成，评估当前进展...", iteration));
        
        // 简单的中间评估
        if (context.isAllActionsCompleted()) {
            context.streamResult("当前行动已完成，准备生成最终答案");
        } else {
            context.streamResult("需要继续执行更多行动");
        }
    }
    
    @Override
    protected int calculateIterations(CognitiveContext context) {
        // ReAct模式的迭代次数基于实际执行的轮数
        return (int) context.getMetadata().getOrDefault("actual_iterations", 1);
    }
}
