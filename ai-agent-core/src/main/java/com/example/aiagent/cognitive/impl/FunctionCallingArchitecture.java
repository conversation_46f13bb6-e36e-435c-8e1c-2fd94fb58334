package com.example.aiagent.cognitive.impl;

import com.example.aiagent.cognitive.BaseCognitiveArchitecture;
import com.example.aiagent.cognitive.components.*;
import com.example.aiagent.cognitive.impl.components.fc.*;
import com.example.aiagent.model.AgentMode;
import com.example.aiagent.model.AgentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Function Calling 认知架构
 * 实现快速、直接的函数调用模式
 */
@Component
public class FunctionCallingArchitecture extends BaseCognitiveArchitecture {
    
    @Autowired
    public FunctionCallingArchitecture(
            FCPerceptionComponent perceptionComponent,
            FCPlanningComponent planningComponent,
            FCActionComponent actionComponent,
            FCReflectionComponent reflectionComponent) {
        super(perceptionComponent, planningComponent, actionComponent, reflectionComponent);
    }
    
    @Override
    public String getArchitectureName() {
        return "Function Calling Architecture";
    }
    
    @Override
    public boolean supports(AgentRequest request) {
        return request.getMode() == AgentMode.FUNCTION_CALLING;
    }
}
