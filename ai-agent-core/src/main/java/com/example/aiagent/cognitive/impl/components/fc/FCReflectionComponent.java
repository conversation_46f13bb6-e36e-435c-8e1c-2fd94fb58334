package com.example.aiagent.cognitive.impl.components.fc;

import com.example.aiagent.cognitive.CognitiveContext;
import com.example.aiagent.cognitive.components.ReflectionComponent;
import com.example.aiagent.model.ChatMessage;
import com.example.aiagent.model.ChatRequest;
import com.example.aiagent.model.ChatResponse;
import com.example.aiagent.service.AiModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Function Calling 反思组件
 * 基于函数执行结果生成最终答案
 */
@Component
public class FCReflectionComponent implements ReflectionComponent {
    
    private static final Logger logger = LoggerFactory.getLogger(FCReflectionComponent.class);
    
    @Autowired
    private AiModelService aiModelService;
    
    @Override
    public void reflect(CognitiveContext context) {
        logger.debug("🤔 FC反思: 生成最终答案");
        
        try {
            // 1. 生成最终答案
            String finalAnswer = generateFinalAnswer(context);
            context.setFinalAnswer(finalAnswer);
            
            // 2. 计算置信度
            double confidence = calculateConfidence(context);
            context.setConfidenceScore(confidence);
            
            // 3. 提取学习经验
            List<String> lessons = extractLessons(context);
            context.setLessonsLearned(lessons);
            
            logger.debug("✅ FC反思完成: 置信度={:.2f}, 经验数={}", confidence, lessons.size());
            
        } catch (Exception e) {
            logger.error("❌ FC反思失败: {}", e.getMessage(), e);
            context.setFinalAnswer("抱歉，处理您的请求时遇到了问题：" + e.getMessage());
            context.setConfidenceScore(0.0);
        }
    }
    
    /**
     * 生成最终答案
     */
    private String generateFinalAnswer(CognitiveContext context) {
        context.streamThought("基于函数执行结果生成最终答案...");
        
        // 如果没有函数调用，直接让AI回答
        if (context.getExecutedActions().isEmpty()) {
            return generateDirectAnswer(context);
        }
        
        // 基于函数执行结果生成答案
        return generateAnswerFromFunctionResults(context);
    }
    
    /**
     * 直接生成答案（无函数调用）
     */
    private String generateDirectAnswer(CognitiveContext context) {
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setMessages(context.getConversation());
        
        ChatResponse response = aiModelService.chat(chatRequest);
        ChatMessage assistantMessage = response.getChoices().get(0).getMessage();
        
        // 添加到对话历史
        context.getConversation().add(assistantMessage);
        
        return assistantMessage.getContent();
    }
    
    /**
     * 基于函数结果生成答案
     */
    private String generateAnswerFromFunctionResults(CognitiveContext context) {
        // 对话历史已经包含了函数调用和结果，直接让AI总结
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setMessages(context.getConversation());
        
        ChatResponse response = aiModelService.chat(chatRequest);
        ChatMessage assistantMessage = response.getChoices().get(0).getMessage();
        
        // 添加到对话历史
        context.getConversation().add(assistantMessage);
        
        return assistantMessage.getContent();
    }
    
    /**
     * 计算置信度
     */
    private double calculateConfidence(CognitiveContext context) {
        double confidence = 0.8; // 基础置信度
        
        // 基于执行成功率调整
        if (context.isAllActionsCompleted()) {
            confidence += 0.15;
        } else {
            confidence -= 0.3;
        }
        
        // 基于意图识别清晰度调整
        String intent = context.getUserIntent();
        if (intent != null && !intent.equals("general_query")) {
            confidence += 0.1;
        }
        
        // 基于实体提取数量调整
        if (!context.getExtractedEntities().isEmpty()) {
            confidence += 0.05;
        }
        
        // 基于函数执行数量调整
        int functionsExecuted = context.getExecutedActions().size();
        if (functionsExecuted > 0) {
            confidence += Math.min(0.1, functionsExecuted * 0.05);
        }
        
        return Math.max(0.0, Math.min(1.0, confidence));
    }
    
    /**
     * 提取学习经验
     */
    private List<String> extractLessons(CognitiveContext context) {
        List<String> lessons = new ArrayList<>();
        
        // 基于意图识别的经验
        String intent = context.getUserIntent();
        if (intent != null) {
            lessons.add("成功识别用户意图: " + intent);
        }
        
        // 基于函数执行的经验
        if (context.isAllActionsCompleted()) {
            lessons.add("所有计划的函数都成功执行");
        } else {
            lessons.add("部分函数执行失败，需要改进错误处理");
        }
        
        // 基于实体提取的经验
        if (!context.getExtractedEntities().isEmpty()) {
            lessons.add("成功提取关键实体: " + context.getExtractedEntities());
        }
        
        // 基于执行策略的经验
        String strategy = context.getExecutionStrategy();
        if (strategy != null) {
            lessons.add("使用执行策略: " + strategy);
        }
        
        return lessons;
    }
    
    @Override
    public String getComponentName() {
        return "FCReflectionComponent";
    }
}
