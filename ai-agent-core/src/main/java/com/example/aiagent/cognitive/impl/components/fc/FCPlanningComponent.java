package com.example.aiagent.cognitive.impl.components.fc;

import com.example.aiagent.cognitive.CognitiveContext;
import com.example.aiagent.cognitive.components.PlanningComponent;
import com.example.aiagent.function.FunctionRegistry;
import com.example.aiagent.model.ToolDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Function Calling 规划组件
 * 基于意图快速选择合适的函数和执行策略
 */
@Component
public class FCPlanningComponent implements PlanningComponent {
    
    private static final Logger logger = LoggerFactory.getLogger(FCPlanningComponent.class);
    
    @Autowired
    private FunctionRegistry functionRegistry;
    
    @Override
    public void plan(CognitiveContext context) {
        logger.debug("📋 FC规划: 制定执行计划");
        
        String intent = context.getUserIntent();
        List<String> entities = context.getExtractedEntities();
        
        // 1. 选择执行策略
        String strategy = selectExecutionStrategy(intent, entities);
        context.setExecutionStrategy(strategy);
        
        // 2. 规划具体行动
        List<String> actions = planActions(intent, entities);
        context.setPlannedActions(actions);
        
        // 3. 选择所需工具
        List<ToolDefinition> tools = selectRequiredTools(intent);
        context.setRequiredTools(tools);
        
        logger.debug("✅ FC规划完成: 策略={}, 行动={}, 工具数={}", strategy, actions, tools.size());
    }
    
    /**
     * 选择执行策略
     */
    private String selectExecutionStrategy(String intent, List<String> entities) {
        switch (intent) {
            case "weather_query":
                return entities.size() > 1 ? "parallel_weather_query" : "single_weather_query";
            case "calculation":
                return "direct_calculation";
            case "time_query":
                return "direct_time_query";
            default:
                return "general_ai_response";
        }
    }
    
    /**
     * 规划具体行动
     */
    private List<String> planActions(String intent, List<String> entities) {
        List<String> actions = new ArrayList<>();
        
        switch (intent) {
            case "weather_query":
                if (entities.isEmpty()) {
                    actions.add("request_city_clarification");
                } else {
                    for (String city : entities) {
                        actions.add("get_weather_for_" + city);
                    }
                }
                break;
                
            case "calculation":
                actions.add("perform_calculation");
                break;
                
            case "time_query":
                actions.add("get_current_time");
                break;
                
            default:
                actions.add("generate_ai_response");
                break;
        }
        
        return actions;
    }
    
    /**
     * 选择所需工具
     */
    private List<ToolDefinition> selectRequiredTools(String intent) {
        List<ToolDefinition> tools = new ArrayList<>();
        List<ToolDefinition> allTools = functionRegistry.getAllToolDefinitions();
        
        switch (intent) {
            case "weather_query":
                // 查找天气相关工具
                allTools.stream()
                    .filter(tool -> tool.getFunction().getName().contains("weather"))
                    .forEach(tools::add);
                break;
                
            case "calculation":
                // 查找计算相关工具
                allTools.stream()
                    .filter(tool -> tool.getFunction().getName().contains("calculate") || 
                                  tool.getFunction().getName().contains("math"))
                    .forEach(tools::add);
                break;
                
            case "time_query":
                // 查找时间相关工具
                allTools.stream()
                    .filter(tool -> tool.getFunction().getName().contains("time") || 
                                  tool.getFunction().getName().contains("date"))
                    .forEach(tools::add);
                break;
                
            default:
                // 对于一般查询，提供所有可用工具
                tools.addAll(allTools);
                break;
        }
        
        return tools;
    }
    
    @Override
    public String getComponentName() {
        return "FCPlanningComponent";
    }
}
