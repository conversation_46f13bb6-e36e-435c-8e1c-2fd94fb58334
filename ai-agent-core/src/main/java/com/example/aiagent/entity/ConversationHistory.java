package com.example.aiagent.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 对话历史记录实体
 */
@Entity
@Table(name = "conversation_history")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 会话ID - 用于关联同一次对话的多轮交互
     */
    @Column(name = "session_id", nullable = false, length = 100)
    private String sessionId;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 100)
    private String userId;
    
    /**
     * 用户角色
     */
    @Column(name = "user_role", length = 50)
    private String userRole;
    
    /**
     * 消息类型: USER, ASSISTANT, SYSTEM, TOOL
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false)
    private MessageType messageType;
    
    /**
     * 消息内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;
    
    /**
     * 工具调用信息 (JSON格式)
     */
    @Column(name = "tool_calls", columnDefinition = "TEXT")
    private String toolCalls;
    
    /**
     * 工具调用ID
     */
    @Column(name = "tool_call_id", length = 100)
    private String toolCallId;
    
    /**
     * 认知架构类型
     */
    @Column(name = "cognitive_architecture", length = 50)
    private String cognitiveArchitecture;
    
    /**
     * 处理模式
     */
    @Column(name = "processing_mode", length = 50)
    private String processingMode;
    
    /**
     * 响应时间(毫秒)
     */
    @Column(name = "response_time_ms")
    private Long responseTimeMs;
    
    /**
     * Token使用统计 (JSON格式)
     */
    @Column(name = "token_usage", columnDefinition = "TEXT")
    private String tokenUsage;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 额外的元数据 (JSON格式)
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        USER,       // 用户消息
        ASSISTANT,  // AI助手消息
        SYSTEM,     // 系统消息
        TOOL        // 工具调用结果
    }
}
