package com.example.aiagent.core;

import com.example.aiagent.cognitive.CognitiveArchitecture;
import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * 执行管道 - 核心流程编排器
 * 负责协调整个AI Agent的执行流程
 */
@Component
public class ExecutionPipeline {
    
    private static final Logger logger = LoggerFactory.getLogger(ExecutionPipeline.class);
    
    private final List<CognitiveArchitecture> cognitiveArchitectures;
    private final RequestPreprocessor preprocessor;
    private final ResponsePostprocessor postprocessor;

    public ExecutionPipeline(List<CognitiveArchitecture> cognitiveArchitectures,
                           RequestPreprocessor preprocessor,
                           ResponsePostprocessor postprocessor) {
        this.cognitiveArchitectures = cognitiveArchitectures;
        this.preprocessor = preprocessor;
        this.postprocessor = postprocessor;
    }
    
    /**
     * 核心执行流程 - 方法调用链
     */
    public AgentResponse execute(AgentRequest request) {
        logger.info("🚀 开始执行管道: {}", request.getMessage());
        
        try {
            // 1. 预处理请求
            AgentRequest processedRequest = preprocessRequest(request);
            
            // 2. 选择认知架构
            CognitiveArchitecture architecture = selectArchitecture(processedRequest);

            // 3. 执行认知处理
            AgentResponse response = executeCognitive(architecture, processedRequest);
            
            // 4. 后处理响应
            AgentResponse finalResponse = postprocessResponse(response, processedRequest);
            
            logger.info("✅ 管道执行完成: 模式={}, 迭代={}", 
                finalResponse.getMode(), finalResponse.getIterations());
            
            return finalResponse;
            
        } catch (Exception e) {
            logger.error("❌ 管道执行失败: {}", e.getMessage(), e);
            return AgentResponse.error("执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 流式执行流程
     */
    public void executeStream(AgentRequest request, SseEmitter emitter) {
        logger.info("🌊 开始流式执行管道: {}", request.getMessage());
        
        try {
            // 1. 预处理请求
            AgentRequest processedRequest = preprocessRequest(request);
            
            // 2. 选择认知架构
            CognitiveArchitecture architecture = selectArchitecture(processedRequest);

            // 3. 流式认知处理
            executeStreamCognitive(architecture, processedRequest, emitter);
            
        } catch (Exception e) {
            logger.error("❌ 流式管道执行失败: {}", e.getMessage(), e);
            try {
                emitter.send("❌ 执行失败: " + e.getMessage());
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
        }
    }
    
    // ==================== 私有方法：清晰的调用链 ====================
    
    /**
     * 步骤1: 预处理请求
     */
    private AgentRequest preprocessRequest(AgentRequest request) {
        logger.debug("📝 预处理请求");
        return preprocessor.process(request);
    }
    
    /**
     * 步骤2: 选择合适的认知架构
     */
    private CognitiveArchitecture selectArchitecture(AgentRequest request) {
        logger.debug("🎯 选择认知架构: 模式={}", request.getMode());

        return cognitiveArchitectures.stream()
            .filter(architecture -> architecture.supports(request))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException(
                "未找到支持模式 " + request.getMode() + " 的认知架构"));
    }

    /**
     * 步骤3: 执行认知处理
     */
    private AgentResponse executeCognitive(CognitiveArchitecture architecture, AgentRequest request) {
        logger.debug("🧠 执行认知处理: 架构={}", architecture.getArchitectureName());
        return architecture.process(request);
    }

    /**
     * 步骤3: 流式认知处理
     */
    private void executeStreamCognitive(CognitiveArchitecture architecture, AgentRequest request, SseEmitter emitter) {
        logger.debug("🌊 流式认知处理: 架构={}", architecture.getArchitectureName());
        architecture.processStream(request, emitter);
    }
    
    /**
     * 步骤4: 后处理响应
     */
    private AgentResponse postprocessResponse(AgentResponse response, AgentRequest request) {
        logger.debug("🔧 后处理响应");
        return postprocessor.process(response, request);
    }
}
