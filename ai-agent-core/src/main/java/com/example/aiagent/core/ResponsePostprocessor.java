package com.example.aiagent.core;

import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import com.example.aiagent.model.AgentMode;
import com.example.aiagent.model.ChatMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 响应后处理器
 * 负责响应的增强、统计和格式化
 */
@Component
public class ResponsePostprocessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ResponsePostprocessor.class);
    
    /**
     * 处理响应 - 增强和统计
     */
    public AgentResponse process(AgentResponse response, AgentRequest request) {
        logger.debug("后处理响应");
        
        // 1. 设置基本信息
        setBasicInfo(response, request);
        
        // 2. 统计使用的函数
        calculateFunctionsUsed(response);
        
        // 3. 计算迭代次数
        calculateIterations(response, request.getMode());
        
        // 4. 验证响应完整性
        validateResponse(response);
        
        return response;
    }
    
    /**
     * 设置基本信息
     */
    private void setBasicInfo(AgentResponse response, AgentRequest request) {
        response.setSessionId(request.getSessionId());
        response.setMode(request.getMode());
        
        if (response.isSuccess() && response.getErrorMessage() == null) {
            response.setSuccess(true);
        }
        
        logger.debug("✅ 基本信息设置完成");
    }
    
    /**
     * 统计使用的函数
     */
    private void calculateFunctionsUsed(AgentResponse response) {
        List<String> functions = new ArrayList<>();
        
        if (response.getConversation() != null) {
            response.getConversation().forEach(message -> {
                if (message.getToolCalls() != null) {
                    message.getToolCalls().forEach(toolCall -> {
                        String functionName = toolCall.getFunction().getName();
                        if (!functions.contains(functionName)) {
                            functions.add(functionName);
                        }
                    });
                }
            });
        }
        
        response.setFunctionsUsed(functions);
        logger.debug("✅ 函数统计完成: {}", functions);
    }
    
    /**
     * 计算迭代次数
     */
    private void calculateIterations(AgentResponse response, AgentMode mode) {
        int iterations = 1; // 默认至少1次迭代
        
        if (response.getConversation() != null) {
            if (mode == AgentMode.FUNCTION_CALLING) {
                // Function Calling模式：通常是2轮（分析+执行+回答）
                iterations = Math.max(1, (response.getConversation().size() - 1) / 2);
            } else {
                // ReAct模式：每轮包含用户消息、助手消息、可能的工具消息
                iterations = (int) response.getConversation().stream()
                    .filter(msg -> "assistant".equals(msg.getRole()))
                    .count();
            }
        }
        
        response.setIterations(iterations);
        logger.debug("✅ 迭代次数计算完成: {} (模式: {})", iterations, mode);
    }
    
    /**
     * 验证响应完整性
     */
    private void validateResponse(AgentResponse response) {
        if (response.getContent() == null || response.getContent().trim().isEmpty()) {
            logger.warn("⚠️ 响应内容为空");
        }
        
        if (response.getConversation() == null || response.getConversation().isEmpty()) {
            logger.warn("⚠️ 对话历史为空");
        }
        
        logger.debug("✅ 响应验证完成");
    }
}
