package com.example.aiagent.core;

import com.example.aiagent.model.AgentRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 请求预处理器
 * 负责请求的标准化、验证和增强
 */
@Component
public class RequestPreprocessor {
    
    private static final Logger logger = LoggerFactory.getLogger(RequestPreprocessor.class);
    
    /**
     * 处理请求 - 标准化和验证
     */
    public AgentRequest process(AgentRequest request) {
        logger.debug("预处理请求: {}", request.getMessage());
        
        // 1. 验证请求
        validateRequest(request);
        
        // 2. 标准化请求
        normalizeRequest(request);
        
        // 3. 增强请求
        enhanceRequest(request);
        
        return request;
    }
    
    /**
     * 验证请求有效性
     */
    private void validateRequest(AgentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        
        if (request.getMessage() == null || request.getMessage().trim().isEmpty()) {
            throw new IllegalArgumentException("消息内容不能为空");
        }
        
        if (request.getMode() == null) {
            throw new IllegalArgumentException("执行模式不能为空");
        }
        
        logger.debug("✅ 请求验证通过");
    }
    
    /**
     * 标准化请求参数
     */
    private void normalizeRequest(AgentRequest request) {
        // 清理消息内容
        String message = request.getMessage().trim();
        request.setMessage(message);
        
        // 设置默认值
        if (request.getMaxIterations() == null) {
            request.setMaxIterations(5);
        }
        
        if (request.getTemperature() == null) {
            request.setTemperature(0.7);
        }
        
        if (request.getMaxTokens() == null) {
            request.setMaxTokens(4000);
        }
        
        logger.debug("✅ 请求标准化完成");
    }
    
    /**
     * 增强请求 - 添加上下文信息
     */
    private void enhanceRequest(AgentRequest request) {
        // 生成会话ID（如果没有）
        if (request.getSessionId() == null) {
            request.setSessionId("session_" + System.currentTimeMillis());
        }
        
        // 可以在这里添加更多增强逻辑
        // 例如：用户偏好、历史上下文等
        
        logger.debug("✅ 请求增强完成: sessionId={}", request.getSessionId());
    }
}
