package com.example.aiagent.service;

import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

/**
 * 城市名称映射服务
 * 将中文城市名映射为英文城市名，用于调用国际天气API
 */
@Service
public class CityNameMappingService {
    
    private final Map<String, String> cityMapping = new HashMap<>();
    
    public CityNameMappingService() {
        initializeCityMapping();
    }
    
    /**
     * 初始化城市名称映射
     */
    private void initializeCityMapping() {
        // 直辖市
        cityMapping.put("北京", "Beijing");
        cityMapping.put("上海", "Shanghai");
        cityMapping.put("天津", "Tianjin");
        cityMapping.put("重庆", "Chongqing");
        
        // 省会城市
        cityMapping.put("广州", "Guangzhou");
        cityMapping.put("深圳", "Shenzhen");
        cityMapping.put("杭州", "Hangzhou");
        cityMapping.put("南京", "Nanjing");
        cityMapping.put("武汉", "Wuhan");
        cityMapping.put("成都", "Chengdu");
        cityMapping.put("西安", "Xi'an");
        cityMapping.put("郑州", "Zhengzhou");
        cityMapping.put("济南", "Jinan");
        cityMapping.put("青岛", "Qingdao");
        cityMapping.put("大连", "Dalian");
        cityMapping.put("沈阳", "Shenyang");
        cityMapping.put("长春", "Changchun");
        cityMapping.put("哈尔滨", "Harbin");
        cityMapping.put("石家庄", "Shijiazhuang");
        cityMapping.put("太原", "Taiyuan");
        cityMapping.put("呼和浩特", "Hohhot");
        cityMapping.put("兰州", "Lanzhou");
        cityMapping.put("西宁", "Xining");
        cityMapping.put("银川", "Yinchuan");
        cityMapping.put("乌鲁木齐", "Urumqi");
        cityMapping.put("拉萨", "Lhasa");
        cityMapping.put("昆明", "Kunming");
        cityMapping.put("贵阳", "Guiyang");
        cityMapping.put("南宁", "Nanning");
        cityMapping.put("海口", "Haikou");
        cityMapping.put("三亚", "Sanya");
        cityMapping.put("福州", "Fuzhou");
        cityMapping.put("厦门", "Xiamen");
        cityMapping.put("南昌", "Nanchang");
        cityMapping.put("长沙", "Changsha");
        cityMapping.put("合肥", "Hefei");
        
        // 港澳台
        cityMapping.put("香港", "Hong Kong");
        cityMapping.put("澳门", "Macau");
        cityMapping.put("台北", "Taipei");
        
        // 国际城市
        cityMapping.put("纽约", "New York");
        cityMapping.put("洛杉矶", "Los Angeles");
        cityMapping.put("伦敦", "London");
        cityMapping.put("巴黎", "Paris");
        cityMapping.put("东京", "Tokyo");
        cityMapping.put("首尔", "Seoul");
        cityMapping.put("新加坡", "Singapore");
        cityMapping.put("悉尼", "Sydney");
        cityMapping.put("墨尔本", "Melbourne");
        cityMapping.put("多伦多", "Toronto");
        cityMapping.put("温哥华", "Vancouver");
        cityMapping.put("柏林", "Berlin");
        cityMapping.put("罗马", "Rome");
        cityMapping.put("马德里", "Madrid");
        cityMapping.put("阿姆斯特丹", "Amsterdam");
        cityMapping.put("莫斯科", "Moscow");
        cityMapping.put("迪拜", "Dubai");
        cityMapping.put("曼谷", "Bangkok");
        cityMapping.put("吉隆坡", "Kuala Lumpur");
        cityMapping.put("雅加达", "Jakarta");
        cityMapping.put("马尼拉", "Manila");
        cityMapping.put("孟买", "Mumbai");
        cityMapping.put("新德里", "New Delhi");
    }
    
    /**
     * 将城市名转换为英文名
     * 如果是中文城市名，返回对应的英文名
     * 如果已经是英文或未找到映射，返回原名称
     */
    public String mapCityName(String cityName) {
        if (cityName == null || cityName.trim().isEmpty()) {
            return cityName;
        }
        
        String trimmedName = cityName.trim();
        
        // 如果存在中文映射，返回英文名
        if (cityMapping.containsKey(trimmedName)) {
            String englishName = cityMapping.get(trimmedName);
            return englishName;
        }
        
        // 如果没有找到映射，返回原名称（可能已经是英文）
        return trimmedName;
    }
    
    /**
     * 检查是否为支持的城市
     */
    public boolean isSupportedCity(String cityName) {
        if (cityName == null || cityName.trim().isEmpty()) {
            return false;
        }
        
        String trimmedName = cityName.trim();
        return cityMapping.containsKey(trimmedName) || isLikelyEnglishCityName(trimmedName);
    }
    
    /**
     * 简单判断是否可能是英文城市名
     */
    private boolean isLikelyEnglishCityName(String cityName) {
        // 简单的英文字符检查
        return cityName.matches("^[a-zA-Z\\s'.-]+$");
    }
    
    /**
     * 获取所有支持的中文城市名
     */
    public java.util.Set<String> getSupportedChineseCities() {
        return cityMapping.keySet();
    }
}
