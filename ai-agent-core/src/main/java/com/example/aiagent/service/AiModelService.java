package com.example.aiagent.service;

import com.example.aiagent.model.ChatRequest;
import com.example.aiagent.model.ChatResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * AI模型服务接口
 */
public interface AiModelService {
    
    /**
     * 发送聊天请求（同步）
     * 
     * @param request 聊天请求
     * @return 聊天响应
     */
    ChatResponse chat(ChatRequest request);
    
    /**
     * 发送聊天请求（异步）
     * 
     * @param request 聊天请求
     * @return 聊天响应的Mono
     */
    Mono<ChatResponse> chatAsync(ChatRequest request);
    
    /**
     * 发送流式聊天请求
     * 
     * @param request 聊天请求
     * @return 聊天响应流
     */
    Flux<ChatResponse> chatStream(ChatRequest request);
    
    /**
     * 检查模型是否可用
     * 
     * @param modelName 模型名称
     * @return 是否可用
     */
    boolean isModelAvailable(String modelName);
    
    /**
     * 获取支持的模型列表
     * 
     * @return 模型名称列表
     */
    java.util.List<String> getSupportedModels();
}