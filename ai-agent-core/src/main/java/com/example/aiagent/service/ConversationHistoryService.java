package com.example.aiagent.service;

import com.example.aiagent.entity.ConversationHistory;
import com.example.aiagent.repository.ConversationHistoryRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 对话历史服务
 */
@Service
@Slf4j
public class ConversationHistoryService {
    
    @Autowired
    private ConversationHistoryRepository conversationHistoryRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 保存用户消息
     */
    @Transactional
    public ConversationHistory saveUserMessage(String sessionId, String userId, String userRole, 
                                             String content, String cognitiveArchitecture, 
                                             String processingMode, Map<String, Object> metadata) {
        ConversationHistory history = ConversationHistory.builder()
                .sessionId(sessionId)
                .userId(userId)
                .userRole(userRole)
                .messageType(ConversationHistory.MessageType.USER)
                .content(content)
                .cognitiveArchitecture(cognitiveArchitecture)
                .processingMode(processingMode)
                .metadata(toJsonString(metadata))
                .build();
        
        ConversationHistory saved = conversationHistoryRepository.save(history);
        log.debug("保存用户消息: sessionId={}, userId={}, content={}", sessionId, userId, content);
        return saved;
    }
    
    /**
     * 保存AI助手消息
     */
    @Transactional
    public ConversationHistory saveAssistantMessage(String sessionId, String userId, String content, 
                                                   String toolCalls, Long responseTimeMs, 
                                                   String tokenUsage, Map<String, Object> metadata) {
        ConversationHistory history = ConversationHistory.builder()
                .sessionId(sessionId)
                .userId(userId)
                .messageType(ConversationHistory.MessageType.ASSISTANT)
                .content(content)
                .toolCalls(toolCalls)
                .responseTimeMs(responseTimeMs)
                .tokenUsage(tokenUsage)
                .metadata(toJsonString(metadata))
                .build();
        
        ConversationHistory saved = conversationHistoryRepository.save(history);
        log.debug("保存AI助手消息: sessionId={}, userId={}, responseTime={}ms", sessionId, userId, responseTimeMs);
        return saved;
    }
    
    /**
     * 保存工具调用结果
     */
    @Transactional
    public ConversationHistory saveToolMessage(String sessionId, String userId, String content, 
                                             String toolCallId, Map<String, Object> metadata) {
        ConversationHistory history = ConversationHistory.builder()
                .sessionId(sessionId)
                .userId(userId)
                .messageType(ConversationHistory.MessageType.TOOL)
                .content(content)
                .toolCallId(toolCallId)
                .metadata(toJsonString(metadata))
                .build();
        
        ConversationHistory saved = conversationHistoryRepository.save(history);
        log.debug("保存工具调用结果: sessionId={}, toolCallId={}", sessionId, toolCallId);
        return saved;
    }
    
    /**
     * 保存错误消息
     */
    @Transactional
    public ConversationHistory saveErrorMessage(String sessionId, String userId, String errorMessage, 
                                              String cognitiveArchitecture, Map<String, Object> metadata) {
        ConversationHistory history = ConversationHistory.builder()
                .sessionId(sessionId)
                .userId(userId)
                .messageType(ConversationHistory.MessageType.SYSTEM)
                .errorMessage(errorMessage)
                .cognitiveArchitecture(cognitiveArchitecture)
                .metadata(toJsonString(metadata))
                .build();
        
        ConversationHistory saved = conversationHistoryRepository.save(history);
        log.warn("保存错误消息: sessionId={}, error={}", sessionId, errorMessage);
        return saved;
    }
    
    /**
     * 获取会话历史
     */
    public List<ConversationHistory> getSessionHistory(String sessionId) {
        return conversationHistoryRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }
    
    /**
     * 获取用户对话历史（分页）
     */
    public Page<ConversationHistory> getUserHistory(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return conversationHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }
    
    /**
     * 获取用户最近的对话记录
     */
    public List<ConversationHistory> getRecentConversations(String userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return conversationHistoryRepository.findRecentConversations(userId, pageable);
    }
    
    /**
     * 获取用户的所有会话ID
     */
    public List<String> getUserSessionIds(String userId) {
        return conversationHistoryRepository.findDistinctSessionIdsByUserId(userId);
    }
    
    /**
     * 获取会话消息数量
     */
    public long getSessionMessageCount(String sessionId) {
        return conversationHistoryRepository.countBySessionId(sessionId);
    }
    
    /**
     * 获取用户在时间范围内的对话数量
     */
    public long getUserConversationCount(String userId, LocalDateTime startTime, LocalDateTime endTime) {
        return conversationHistoryRepository.countUserConversationsInTimeRange(userId, startTime, endTime);
    }
    
    /**
     * 清理过期的历史记录
     */
    @Transactional
    public void cleanupOldHistory(LocalDateTime cutoffTime) {
        conversationHistoryRepository.deleteByCreatedAtBefore(cutoffTime);
        log.info("清理了 {} 之前的历史记录", cutoffTime);
    }
    
    /**
     * 获取认知架构使用统计
     */
    public Map<String, Long> getCognitiveArchitectureStats() {
        List<Object[]> results = conversationHistoryRepository.countByCognitiveArchitecture();
        return results.stream()
                .collect(java.util.stream.Collectors.toMap(
                    result -> (String) result[0],
                    result -> (Long) result[1]
                ));
    }
    
    /**
     * 更新响应时间和Token使用情况
     */
    @Transactional
    public void updateResponseMetrics(Long historyId, Long responseTimeMs, String tokenUsage) {
        conversationHistoryRepository.findById(historyId).ifPresent(history -> {
            history.setResponseTimeMs(responseTimeMs);
            history.setTokenUsage(tokenUsage);
            conversationHistoryRepository.save(history);
        });
    }
    
    /**
     * 将对象转换为JSON字符串
     */
    private String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("转换为JSON失败: {}", e.getMessage());
            return obj.toString();
        }
    }
    
    /**
     * 从JSON字符串解析对象
     */
    public <T> T fromJsonString(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.warn("从JSON解析失败: {}", e.getMessage());
            return null;
        }
    }
}
