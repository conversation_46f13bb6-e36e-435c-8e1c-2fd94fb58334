package com.example.aiagent.service;

import com.example.aiagent.core.ExecutionPipeline;
import com.example.aiagent.model.AgentRequest;
import com.example.aiagent.model.AgentResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI Agent 服务
 * 作为ExecutionPipeline的简单包装器，提供统一的服务接口
 */
@Service
public class AgentService {

    private static final Logger logger = LoggerFactory.getLogger(AgentService.class);

    @Autowired
    private ExecutionPipeline executionPipeline;

    /**
     * 处理用户请求
     */
    public AgentResponse processRequest(AgentRequest request) {
        logger.info("📨 收到用户请求: {} (模式: {})", request.getMessage(), request.getMode());
        return executionPipeline.execute(request);
    }


    /**
     * 流式处理用户请求
     */
    public void processRequestStream(AgentRequest request, SseEmitter emitter) {
        logger.info("🌊 收到流式请求: {} (模式: {})", request.getMessage(), request.getMode());
        executionPipeline.executeStream(request, emitter);
    }
}