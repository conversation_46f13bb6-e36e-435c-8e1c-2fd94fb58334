package com.example.aiagent.service.impl;

import com.example.aiagent.config.AiModelConfig;
import com.example.aiagent.exception.AiModelException;
import com.example.aiagent.model.ChatMessage;
import com.example.aiagent.model.ChatRequest;
import com.example.aiagent.model.ChatResponse;
import com.example.aiagent.service.AiModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;

/**
 * 阿里云AI模型服务实现
 */
@Service
public class AlibabaAiModelService implements AiModelService {
    
    private static final Logger logger = LoggerFactory.getLogger(AlibabaAiModelService.class);
    
    private final WebClient webClient;
    private final AiModelConfig aiModelConfig;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public AlibabaAiModelService(AiModelConfig aiModelConfig, ObjectMapper objectMapper) {
        this.aiModelConfig = aiModelConfig;
        this.objectMapper = objectMapper;
        
        // 添加配置信息日志
        logger.info("=== AlibabaAiModelService 初始化 ===");
        logger.info("Provider: {}", aiModelConfig.getProvider());
        logger.info("Endpoint: {}", aiModelConfig.getEndpoint());
        logger.info("Model: {}", aiModelConfig.getModelName());
        logger.info("Max Tokens: {}", aiModelConfig.getMaxTokens());
        logger.info("Temperature: {}", aiModelConfig.getTemperature());
        logger.info("Timeout: {}s", aiModelConfig.getTimeoutSeconds());
        
        this.webClient = WebClient.builder()
            .baseUrl(aiModelConfig.getEndpoint())
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + aiModelConfig.getApiKey())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.USER_AGENT, "AI-Agent-Framework/1.0.0")
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }
    
    @Override
    public ChatResponse chat(ChatRequest request) {
        return chatAsync(request).block();
    }
    
    @Override
    public Mono<ChatResponse> chatAsync(ChatRequest request) {
        return sendRequest(request)
            .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                .filter(this::isRetryableException))
            .doOnSubscribe(subscription -> 
                logger.debug("发送聊天请求: model={}, messages={}", 
                    request.getModel(), request.getMessages().size()))
            .doOnSuccess(response -> 
                logger.debug("收到聊天响应: id={}, usage={}", 
                    response.getId(), response.getUsage()))
            .doOnError(error -> 
                logger.error("聊天请求失败: {}", error.getMessage(), error));
    }
    
    @Override
    public Flux<ChatResponse> chatStream(ChatRequest request) {
        // 设置流式请求
        request.setStream(true);
        
        return webClient.post()
            .uri("/chat/completions")
            .bodyValue(request)
            .retrieve()
            .bodyToFlux(String.class)
            .filter(line -> line.startsWith("data: ") && !line.equals("data: [DONE]"))
            .map(line -> line.substring(6)) // 移除 "data: " 前缀
            .flatMap(this::parseStreamResponse)
            .doOnSubscribe(subscription -> 
                logger.debug("开始流式聊天请求: model={}", request.getModel()))
            .doOnComplete(() -> 
                logger.debug("流式聊天请求完成"))
            .doOnError(error -> 
                logger.error("流式聊天请求失败: {}", error.getMessage(), error));
    }
    
    @Override
    public boolean isModelAvailable(String modelName) {
        return getSupportedModels().contains(modelName);
    }
    
    @Override
    public List<String> getSupportedModels() {
        // 阿里云支持的模型列表
        return List.of(
            "deepseek-v3",
            "qwen-plus", 
            "qwen-turbo",
            "qwen-max",
            "qwen-max-longcontext",
            "baichuan2-turbo",
            "yi-large"
        );
    }
    
    /**
     * 发送请求到阿里云API
     */
    private Mono<ChatResponse> sendRequest(ChatRequest request) {
        validateRequest(request);
        
        // 设置默认值
        if (request.getModel() == null) {
            request.setModel(aiModelConfig.getModelName());
        }
        if (request.getMaxTokens() == null) {
            request.setMaxTokens(aiModelConfig.getMaxTokens());
        }
        if (request.getTemperature() == null) {
            request.setTemperature(aiModelConfig.getTemperature());
        }
        
        // 在 sendRequest 方法中，修改工具设置逻辑
        if (request.getTools() != null && !request.getTools().isEmpty()) {
            // 添加 tool_choice 参数
            request.setToolChoice("auto");  // 需要添加这个字段到 ChatRequest
            logger.info("设置工具数量: {}", request.getTools().size());
            logger.info("工具详情: {}", request.getTools());
        } else {
            request.setTools(null);
            logger.info("没有可用工具");
        }
        
        // 添加详细日志输出
        logger.info("=== API请求详情 ===");
        logger.info("Base URL: {}", aiModelConfig.getEndpoint());
        logger.info("完整请求URL: {}/chat/completions", aiModelConfig.getEndpoint());
        logger.info("API Key: {}***{}", 
            aiModelConfig.getApiKey().substring(0, Math.min(10, aiModelConfig.getApiKey().length())),
            aiModelConfig.getApiKey().substring(Math.max(0, aiModelConfig.getApiKey().length() - 4)));
        logger.info("Model: {}", request.getModel());
        logger.info("Messages count: {}", request.getMessages().size());
        
        try {
            String requestJson = objectMapper.writeValueAsString(request);
            logger.info("请求体: {}", requestJson);
        } catch (Exception e) {
            logger.warn("无法序列化请求体: {}", e.getMessage());
        }
        
        return webClient.post()
            .uri("/chat/completions")
            .bodyValue(request)
            .retrieve()
            .onStatus(status -> status.isError(), this::handleErrorResponse)
            .bodyToMono(ChatResponse.class)
            .doOnSuccess(response -> {
                logger.info("API请求成功");
                if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                    ChatResponse.Choice choice = response.getChoices().get(0);
                    ChatMessage message = choice.getMessage();  // ✅ 正确：使用 ChatMessage
                    logger.info("响应内容: {}", message.getContent());
                    // 添加这些关键日志
                    logger.info("工具调用数量: {}", message.getToolCalls() != null ? message.getToolCalls().size() : 0);
                    logger.info("工具调用详情: {}", message.getToolCalls());
                    logger.info("完整响应: {}", response);
                }
            })
            .doOnError(error -> {
                logger.error("API请求失败: {}", error.getMessage());
            })
            .timeout(Duration.ofSeconds(aiModelConfig.getTimeoutSeconds()));
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(ChatRequest request) {
        if (request == null) {
            throw AiModelException.invalidParameter("request", "请求不能为空");
        }
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw AiModelException.invalidParameter("messages", "消息列表不能为空");
        }
        if (request.getModel() != null && !isModelAvailable(request.getModel())) {
            throw AiModelException.modelUnavailable(request.getModel());
        }
    }
    
    /**
     * 处理错误响应
     */
    private Mono<Throwable> handleErrorResponse(org.springframework.web.reactive.function.client.ClientResponse response) {
        return response.bodyToMono(String.class)
            .map(errorBody -> {
                logger.error("API请求失败: status={}, body={}", response.statusCode(), errorBody);
                
                switch (response.statusCode().value()) {
                    case 401:
                        return AiModelException.invalidApiKey();
                    case 429:
                        return AiModelException.rateLimitExceeded();
                    case 503:
                        return AiModelException.modelUnavailable("unknown");
                    default:
                        return new AiModelException(
                            "API_ERROR", 
                            String.format("API请求失败: %s", errorBody), 
                            response.statusCode().value()
                        );
                }
            });
    }
    
    /**
     * 解析流式响应
     */
    private Mono<ChatResponse> parseStreamResponse(String jsonLine) {
        try {
            return Mono.just(objectMapper.readValue(jsonLine, ChatResponse.class));
        } catch (Exception e) {
            logger.warn("解析流式响应失败: {}", jsonLine, e);
            return Mono.empty();
        }
    }
    
    /**
     * 判断是否为可重试的异常
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            return ex.getStatusCode().is5xxServerError() || 
                   ex.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS;
        }
        return throwable instanceof java.net.ConnectException ||
               throwable instanceof java.util.concurrent.TimeoutException;
    }
}
