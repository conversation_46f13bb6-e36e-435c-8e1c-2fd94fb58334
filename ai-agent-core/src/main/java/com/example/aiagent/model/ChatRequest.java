package com.example.aiagent.model;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ChatRequest {
    private List<ChatMessage> messages;
    private String model;
    
    @JsonProperty("max_tokens")
    private Integer maxTokens;
    
    @JsonProperty("tool_choice")
    private String toolChoice;
    private Boolean stream;
    private List<ToolDefinition> tools;
    private Double temperature;
    
    
}