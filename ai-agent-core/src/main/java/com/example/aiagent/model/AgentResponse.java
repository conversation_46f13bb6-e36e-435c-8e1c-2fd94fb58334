package com.example.aiagent.model;

import lombok.Data;
import java.util.List;

@Data
public class AgentResponse {
    private String content;
    private List<ChatMessage> conversation;
    private boolean success;
    private String sessionId;
    private AgentMode mode;
    private List<String> functionsUsed;
    private Integer iterations;
    private String errorMessage;
    
    public AgentResponse() {}
    
    public AgentResponse(String content, List<ChatMessage> conversation) {
        this.content = content;
        this.conversation = conversation;
        this.success = true;
    }
    
    public static AgentResponse error(String errorMessage) {
        AgentResponse response = new AgentResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        return response;
    }
}