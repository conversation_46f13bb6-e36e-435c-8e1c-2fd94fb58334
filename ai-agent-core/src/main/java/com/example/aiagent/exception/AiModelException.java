package com.example.aiagent.exception;

public class AiModelException extends RuntimeException {
    private final String errorCode;
    private final int statusCode;
    
    public AiModelException(String errorCode, String message, int statusCode) {
        super(message);
        this.errorCode = errorCode;
        this.statusCode = statusCode;
    }
    
    public static AiModelException invalidParameter(String parameter, String message) {
        return new AiModelException("INVALID_PARAMETER", 
            String.format("Invalid parameter %s: %s", parameter, message), 400);
    }
    
    public static AiModelException invalidApiKey() {
        return new AiModelException("INVALID_API_KEY", "Invalid API key", 401);
    }
    
    public static AiModelException rateLimitExceeded() {
        return new AiModelException("RATE_LIMIT_EXCEEDED", "Rate limit exceeded", 429);
    }
    
    public static AiModelException modelUnavailable(String model) {
        return new AiModelException("MODEL_UNAVAILABLE", 
            String.format("Model %s is unavailable", model), 503);
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
}