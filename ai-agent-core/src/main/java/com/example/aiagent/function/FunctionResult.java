package com.example.aiagent.function;

import lombok.Data;

@Data
public class FunctionResult {
    private String status;
    private String result;
    private String error;
    
    public FunctionResult() {}
    
    public FunctionResult(String status, String result) {
        this.status = status;
        this.result = result;
    }
    
    // 添加静态方法用于创建错误结果
    public static FunctionResult error(String errorMessage) {
        FunctionResult result = new FunctionResult();
        result.setStatus("error");
        result.setError(errorMessage);
        return result;
    }
    
    // 添加成功检查方法
    public boolean isSuccess() {
        return "success".equals(status);
    }
    
    @Override
    public String toString() {
        return result != null ? result : error;
    }
}