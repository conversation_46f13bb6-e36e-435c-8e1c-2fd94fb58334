package com.example.aiagent.function;

import com.example.aiagent.model.ToolDefinition;
import com.example.aiagent.mcp.McpFunctionAdapter;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class FunctionRegistry {
    
    private final Map<String, McpFunctionAdapter> registeredFunctions = new ConcurrentHashMap<>();
    
    public List<ToolDefinition> getAllToolDefinitions() {
        List<ToolDefinition> tools = new ArrayList<>();

        // 从注册的 MCP 适配器生成工具定义
        for (McpFunctionAdapter adapter : registeredFunctions.values()) {
            ToolDefinition tool = createToolDefinition(adapter);
            tools.add(tool);
        }

        return tools;
    }

    public List<String> getAllFunctionNames() {
        return new ArrayList<>(registeredFunctions.keySet());
    }
    
    private ToolDefinition createToolDefinition(McpFunctionAdapter adapter) {
        ToolDefinition tool = new ToolDefinition();
        tool.setType("function");
        
        ToolDefinition.Function function = new ToolDefinition.Function();
        function.setName(adapter.getToolName());
        
        // 根据工具名称设置描述和参数
        if ("get_weather".equals(adapter.getToolName())) {
            function.setDescription("获取指定城市的天气信息");
            
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("type", "object");
            
            Map<String, Object> properties = new HashMap<>();
            Map<String, Object> cityParam = new HashMap<>();
            cityParam.put("type", "string");
            cityParam.put("description", "城市名称，例如：北京、上海、天津");
            properties.put("city", cityParam);
            
            parameters.put("properties", properties);
            parameters.put("required", List.of("city"));
            
            function.setParameters(parameters);
        }
        
        tool.setFunction(function);
        return tool;
    }
    
    public void registerFunction(McpFunctionAdapter adapter) {
        registeredFunctions.put(adapter.getToolName(), adapter);
    }
    
    public McpFunctionAdapter getFunction(String name) {
        return registeredFunctions.get(name);
    }
}