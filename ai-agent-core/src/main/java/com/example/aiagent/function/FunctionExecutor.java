package com.example.aiagent.function;

import com.example.aiagent.model.ToolCall;
import com.example.aiagent.model.ToolDefinition;
import com.example.aiagent.mcp.McpFunctionAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class FunctionExecutor {
    
    @Autowired
    private FunctionRegistry functionRegistry;
    
    public FunctionResult execute(ToolCall toolCall) {
        String functionName = toolCall.getFunction().getName();
        
        // 从 FunctionRegistry 获取 MCP 适配器
        McpFunctionAdapter adapter = functionRegistry.getFunction(functionName);
        
        if (adapter != null) {
            // 调用 MCP 适配器执行实际的 MCP 调用
            return adapter.execute(toolCall);
        } else {
            // 如果没找到对应的 MCP 适配器，返回基础实现
            return new FunctionResult("success", "Function executed: " + functionName);
        }
    }
    
    public List<ToolDefinition> getAvailableTools() {
        // 从 FunctionRegistry 获取所有工具定义
        return functionRegistry.getAllToolDefinitions();
    }
}