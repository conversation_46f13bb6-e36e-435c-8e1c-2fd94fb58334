#!/bin/bash

# 测试天气 MCP 服务器的真实 API 调用
echo "=== 测试天气 MCP 服务器 (真实 API) ==="

# 检查服务器是否运行
echo "1. 检查天气 MCP 服务器状态..."
if ! lsof -i :8081 > /dev/null 2>&1; then
    echo "❌ 天气 MCP 服务器未运行在端口 8081"
    echo "请先启动服务器: ./start-weather-server.sh"
    exit 1
fi
echo "✅ 天气 MCP 服务器正在运行"

# 测试工具列表
echo "\n2. 测试工具列表..."
tools_response=$(curl -s -X POST http://localhost:8081/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "1",
    "method": "tools/list"
  }')

echo "工具列表响应:"
echo "$tools_response" | jq .

# 检查是否有 get_weather 工具
if echo "$tools_response" | jq -e '.result.tools[] | select(.name == "get_weather")' > /dev/null; then
    echo "✅ get_weather 工具可用"
else
    echo "❌ get_weather 工具不可用"
    exit 1
fi

# 测试真实天气查询 - 北京
echo "\n3. 测试真实天气查询 (北京)..."
weather_response=$(curl -s -X POST http://localhost:8081/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "2",
    "method": "tools/call",
    "params": {
      "name": "get_weather",
      "arguments": {
        "city": "Beijing",
        "units": "metric"
      }
    }
  }')

echo "北京天气响应:"
echo "$weather_response" | jq .

# 检查是否成功获取天气数据
if echo "$weather_response" | jq -e '.result.content[0].text' > /dev/null; then
    echo "✅ 成功获取北京天气数据"
    weather_data=$(echo "$weather_response" | jq -r '.result.content[0].text')
    echo "天气数据: $weather_data"
else
    echo "❌ 获取北京天气数据失败"
    if echo "$weather_response" | jq -e '.error' > /dev/null; then
        error_msg=$(echo "$weather_response" | jq -r '.error.message')
        echo "错误信息: $error_msg"
    fi
fi

# 测试其他城市
echo "\n4. 测试其他城市天气..."
cities=("Shanghai" "Guangzhou" "Shenzhen")

for city in "${cities[@]}"; do
    echo "\n测试 $city 天气:"
    city_response=$(curl -s -X POST http://localhost:8081/mcp \
      -H "Content-Type: application/json" \
      -d "{
        \"jsonrpc\": \"2.0\",
        \"id\": \"3\",
        \"method\": \"tools/call\",
        \"params\": {
          \"name\": \"get_weather\",
          \"arguments\": {
            \"city\": \"$city\",
            \"units\": \"metric\"
          }
        }
      }")
    
    if echo "$city_response" | jq -e '.result.content[0].text' > /dev/null; then
        weather_data=$(echo "$city_response" | jq -r '.result.content[0].text')
        echo "✅ $city: $weather_data"
    else
        echo "❌ $city: 获取失败"
        if echo "$city_response" | jq -e '.error' > /dev/null; then
            error_msg=$(echo "$city_response" | jq -r '.error.message')
            echo "   错误: $error_msg"
        fi
    fi
done

echo "\n=== 测试完成 ==="