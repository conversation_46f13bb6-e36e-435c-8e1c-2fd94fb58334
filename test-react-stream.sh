#!/bin/bash

# 流式ReAct测试脚本
# 模拟ChatGPT式的持续输出体验

echo "🚀 AI Agent 流式ReAct模式测试"
echo "================================"
echo ""

# 检查服务是否运行
if ! curl -s http://localhost:8080/api/agent/debug/tools > /dev/null; then
    echo "❌ Agent服务未运行，请先启动服务"
    exit 1
fi

echo "✅ Agent服务运行正常"
echo ""

# 获取用户输入
if [ -z "$1" ]; then
    echo "请输入您的问题（或使用默认问题）："
    echo "1. 北京天气如何"
    echo "2. 上海和深圳哪个城市天气更好"
    echo "3. 自定义问题"
    echo ""
    read -p "选择 (1-3): " choice
    
    case $choice in
        1)
            question="北京天气如何"
            ;;
        2)
            question="上海和深圳哪个城市天气更好，帮我分析一下"
            ;;
        3)
            read -p "请输入您的问题: " question
            ;;
        *)
            question="北京天气如何"
            ;;
    esac
else
    question="$1"
fi

echo ""
echo "🤖 AI正在思考您的问题: $question"
echo "================================"
echo ""

# 调用流式API并美化输出
curl -N -X POST http://localhost:8080/api/agent/chat/react-stream \
  -H "Content-Type: application/json" \
  -d "{\"message\": \"$question\"}" \
  2>/dev/null | \
  sed 's/^data://g' | \
  while IFS= read -r line; do
    if [ ! -z "$line" ]; then
      echo "$line"
    fi
    # 添加小延迟以模拟打字效果
    sleep 0.1
  done

echo ""
echo "================================"
echo "✅ 思考完成！"
