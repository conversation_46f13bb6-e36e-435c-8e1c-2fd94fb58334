#!/bin/bash

echo "🗄️ 设置AI Agent PostgreSQL数据库"
echo "================================="

# 检查PostgreSQL是否安装
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL未安装，请先安装PostgreSQL"
    echo "macOS: brew install postgresql"
    echo "Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    exit 1
fi

# 检查PostgreSQL服务是否运行
if ! pg_isready -q; then
    echo "❌ PostgreSQL服务未运行，请启动PostgreSQL服务"
    echo "macOS: brew services start postgresql"
    echo "Ubuntu: sudo systemctl start postgresql"
    exit 1
fi

echo "✅ PostgreSQL服务正在运行"

# 数据库配置
DB_NAME="ai_agent_db"
DB_USER="ai_agent_user"
DB_PASSWORD="ai_agent_password"

echo "📋 数据库配置:"
echo "  数据库名: $DB_NAME"
echo "  用户名: $DB_USER"
echo "  密码: $DB_PASSWORD"
echo ""

# 创建数据库用户
echo "👤 创建数据库用户..."
sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || echo "用户可能已存在"

# 创建数据库
echo "🗄️ 创建数据库..."
sudo -u postgres psql -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" 2>/dev/null || echo "数据库可能已存在"

# 授予权限
echo "🔐 授予用户权限..."
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
sudo -u postgres psql -d $DB_NAME -c "GRANT ALL ON SCHEMA public TO $DB_USER;"

# 测试连接
echo "🔗 测试数据库连接..."
if PGPASSWORD=$DB_PASSWORD psql -h localhost -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ 数据库连接测试成功"
else
    echo "❌ 数据库连接测试失败"
    exit 1
fi

echo ""
echo "🎉 数据库设置完成！"
echo ""
echo "📋 连接信息:"
echo "  主机: localhost"
echo "  端口: 5432"
echo "  数据库: $DB_NAME"
echo "  用户名: $DB_USER"
echo "  密码: $DB_PASSWORD"
echo ""
echo "🔗 连接命令:"
echo "  PGPASSWORD=$DB_PASSWORD psql -h localhost -U $DB_USER -d $DB_NAME"
echo ""
echo "📝 应用配置已在 ai-agent-core/src/main/resources/application.yml 中设置"
echo "🚀 现在可以启动应用，Flyway将自动创建表结构"
