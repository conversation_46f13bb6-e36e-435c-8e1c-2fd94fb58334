#!/bin/bash

echo "🔍 测试 PostgreSQL 连接..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 检查服务状态
echo "📊 PostgreSQL 服务状态："
brew services list | grep postgresql

echo ""
echo "🔗 测试数据库连接："

# 测试连接
if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status;" 2>/dev/null; then
    echo "✅ 数据库连接测试成功！"
    
    echo ""
    echo "📋 数据库信息："
    psql testdb -c "
    SELECT 
        current_database() as database_name,
        current_user as username,
        version() as version;
    "
    
    echo ""
    echo "🗂️ 已安装的扩展："
    psql testdb -c "SELECT extname as extension_name FROM pg_extension;"
    
    echo ""
    echo "📝 连接配置信息："
    echo "  主机: localhost (127.0.0.1)"
    echo "  端口: 5432"
    echo "  数据库: testdb"
    echo "  用户名: $(whoami)"
    echo "  密码: (无密码)"
    echo ""
    echo "🔧 管理命令："
    echo "  启动服务: brew services start postgresql@15"
    echo "  停止服务: brew services stop postgresql@15"
    echo "  重启服务: brew services restart postgresql@15"
    echo "  连接数据库: psql testdb"
    echo "  查看日志: brew services info postgresql@15"
    
else
    echo "❌ 数据库连接失败"
    echo "🔧 尝试启动服务："
    brew services start postgresql@15
    echo "⏳ 等待服务启动..."
    sleep 3
    
    if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status;" 2>/dev/null; then
        echo "✅ 服务启动后连接成功！"
    else
        echo "❌ 连接仍然失败，请检查："
        echo "  1. PostgreSQL 服务是否正在运行"
        echo "  2. 数据库 testdb 是否存在"
        echo "  3. 用户权限是否正确"
    fi
fi
