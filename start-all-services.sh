#!/bin/bash

echo "🚀 启动完整的 AI Agent 服务栈..."
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -i:$port > /dev/null 2>&1; then
        log_warning "$service_name 端口 $port 已被占用"
        PID=$(lsof -ti:$port)
        log_info "正在停止进程 PID: $PID"
        kill -9 $PID 2>/dev/null
        sleep 2
        
        if lsof -i:$port > /dev/null 2>&1; then
            log_error "无法停止端口 $port 上的进程"
            return 1
        else
            log_success "已停止端口 $port 上的进程"
        fi
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 启动成功！"
            return 0
        fi
        
        if [ $((attempt % 5)) -eq 0 ]; then
            log_info "等待 $service_name 启动... ($attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 启动超时"
    return 1
}

# 1. 检查 PostgreSQL 数据库
log_info "检查 PostgreSQL 数据库状态..."
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin

if ! psql -h localhost -U sunyilin testdb -c "SELECT 1;" > /dev/null 2>&1; then
    log_warning "PostgreSQL 未运行，正在启动..."
    brew services start postgresql@15
    sleep 5
    
    if ! psql -h localhost -U sunyilin testdb -c "SELECT 1;" > /dev/null 2>&1; then
        log_error "PostgreSQL 启动失败"
        exit 1
    fi
fi
log_success "PostgreSQL 数据库运行正常"

# 2. 检查 Java 环境
log_info "检查 Java 环境..."
if ! java -version > /dev/null 2>&1; then
    log_error "未找到 Java，请确保已安装 Java 17 或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1)
log_success "Java 环境正常: $JAVA_VERSION"

# 3. 检查 Maven 环境
log_info "检查 Maven 环境..."
if ! mvn -version > /dev/null 2>&1; then
    log_error "未找到 Maven，请确保已安装 Maven"
    exit 1
fi

MAVEN_VERSION=$(mvn -version 2>&1 | head -n 1)
log_success "Maven 环境正常: $MAVEN_VERSION"

# 4. 停止现有服务
log_info "停止现有服务..."
check_port 8081 "MCP Server"
check_port 8080 "AI Agent Core"

# 5. 构建并启动 MCP 服务器
log_info "构建并启动 MCP 服务器..."
cd weather-mcp-server

# 检查并构建 MCP 服务器
if [ ! -f "target/weather-mcp-server-1.0.0.jar" ]; then
    log_info "构建 MCP 服务器..."
    if ! mvn clean package -DskipTests -q; then
        log_error "MCP 服务器构建失败"
        exit 1
    fi
    log_success "MCP 服务器构建完成"
fi

# 启动 MCP 服务器
log_info "启动 MCP 服务器 (端口 8081)..."
export OPENWEATHER_API_KEY="********************************"

nohup java -Dloader.main=com.example.aiagent.mcp.server.WeatherMcpServerApplication \
     -jar target/weather-mcp-server-1.0.0.jar \
     --server.port=8081 \
     --spring.application.name=weather-mcp-server > ../mcp-server.log 2>&1 &

MCP_PID=$!
log_info "MCP 服务器启动中，PID: $MCP_PID"

# 等待 MCP 服务器启动
if ! wait_for_service "http://localhost:8081/mcp/health" "MCP Server"; then
    log_error "MCP 服务器启动失败，查看日志: tail -f mcp-server.log"
    exit 1
fi

# 6. 构建并启动 AI Agent 核心服务
cd ..
log_info "构建并启动 AI Agent 核心服务..."

# 检查并构建 AI Agent 核心服务
if [ ! -f "ai-agent-core/target/ai-agent-core-1.0.0.jar" ]; then
    log_info "构建 AI Agent 核心服务..."
    cd ai-agent-core
    if ! mvn clean package -DskipTests -q; then
        log_error "AI Agent 核心服务构建失败"
        exit 1
    fi
    cd ..
    log_success "AI Agent 核心服务构建完成"
fi

# 启动 AI Agent 核心服务
log_info "启动 AI Agent 核心服务 (端口 8080)..."
cd ai-agent-core

nohup java -jar target/ai-agent-core-1.0.0.jar \
     --server.port=8080 \
     --spring.application.name=ai-agent-core \
     --spring.profiles.active=dev \
     --spring.datasource.url=*************************************** \
     --spring.datasource.username=sunyilin \
     --spring.datasource.password=sunyilin \
     --mcp.server.url=http://localhost:8081/mcp > ../ai-agent-core.log 2>&1 &

AGENT_PID=$!
log_info "AI Agent 核心服务启动中，PID: $AGENT_PID"

# 等待 AI Agent 服务启动
cd ..
if ! wait_for_service "http://localhost:8080/actuator/health" "AI Agent Core"; then
    log_error "AI Agent 核心服务启动失败，查看日志: tail -f ai-agent-core.log"
    exit 1
fi

# 7. 服务启动完成
echo ""
echo "🎉 所有服务启动完成！"
echo "=================================="
echo ""
log_success "服务状态:"
echo "  📊 PostgreSQL 数据库: localhost:5432 (运行中)"
echo "  🌤️  MCP 服务器: http://localhost:8081 (PID: $MCP_PID)"
echo "  🤖 AI Agent 核心: http://localhost:8080 (PID: $AGENT_PID)"
echo ""
echo "📋 服务端点:"
echo "  健康检查: http://localhost:8080/actuator/health"
echo "  工具列表: http://localhost:8080/api/agent/debug/tools"
echo "  聊天接口: http://localhost:8080/api/agent/chat"
echo "  MCP 健康检查: http://localhost:8081/mcp/health"
echo ""
echo "📝 日志文件:"
echo "  MCP 服务器: tail -f mcp-server.log"
echo "  AI Agent: tail -f ai-agent-core.log"
echo ""
echo "🛑 停止服务:"
echo "  停止所有: ./stop-all.sh"
echo "  或手动: kill $MCP_PID $AGENT_PID"
echo ""
echo "🧪 测试命令:"
echo "  curl http://localhost:8080/actuator/health"
echo "  curl -X POST http://localhost:8080/api/agent/chat -H 'Content-Type: application/json' -d '{\"message\":\"你好\",\"conversationId\":\"test\"}'"
