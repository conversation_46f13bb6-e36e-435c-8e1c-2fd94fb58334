#!/bin/bash

# PostgreSQL 管理脚本
# 用于管理本地 PostgreSQL@15 服务

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD="sunyilin"

show_help() {
    echo "🗄️  PostgreSQL 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动 PostgreSQL 服务"
    echo "  stop      停止 PostgreSQL 服务"
    echo "  restart   重启 PostgreSQL 服务"
    echo "  status    查看服务状态"
    echo "  connect   连接到 testdb 数据库"
    echo "  test      测试数据库连接"
    echo "  info      显示数据库信息"
    echo "  logs      查看服务日志"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 connect  # 连接数据库"
    echo "  $0 test     # 测试连接"
}

start_service() {
    echo "🚀 启动 PostgreSQL 服务..."
    brew services start postgresql@15
    echo "⏳ 等待服务启动..."
    sleep 3
    show_status
}

stop_service() {
    echo "🛑 停止 PostgreSQL 服务..."
    brew services stop postgresql@15
    show_status
}

restart_service() {
    echo "🔄 重启 PostgreSQL 服务..."
    brew services restart postgresql@15
    echo "⏳ 等待服务重启..."
    sleep 3
    show_status
}

show_status() {
    echo "📊 PostgreSQL 服务状态："
    brew services list | grep postgresql
    echo ""
    
    # 尝试连接测试
    if psql testdb -c "SELECT 'Service is running' as status;" >/dev/null 2>&1; then
        echo "✅ 数据库服务正常运行"
    else
        echo "❌ 数据库服务未运行或连接失败"
    fi
}

connect_db() {
    echo "🔗 连接到 testdb 数据库..."
    echo "提示: 使用 \\q 退出数据库连接"
    echo ""
    psql testdb
}

test_connection() {
    echo "🔍 测试数据库连接..."
    if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status, now() as current_time;" 2>/dev/null; then
        echo "✅ 数据库连接测试成功！"
    else
        echo "❌ 数据库连接失败"
        echo "尝试启动服务..."
        start_service
    fi
}

show_info() {
    echo "📋 PostgreSQL 数据库信息："
    echo ""
    psql testdb -c "
    SELECT 
        current_database() as database_name,
        current_user as username,
        inet_server_addr() as server_address,
        inet_server_port() as server_port,
        version() as version;
    "
    
    echo ""
    echo "🗂️ 已安装的扩展："
    psql testdb -c "SELECT extname as extension_name, extversion as version FROM pg_extension;"
    
    echo ""
    echo "📝 连接配置信息："
    echo "  主机: localhost (127.0.0.1)"
    echo "  端口: 5432"
    echo "  数据库: testdb"
    echo "  用户名: $(whoami)"
    echo "  密码: (无密码)"
}

show_logs() {
    echo "📝 PostgreSQL 服务日志："
    brew services info postgresql@15
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    connect)
        connect_db
        ;;
    test)
        test_connection
        ;;
    info)
        show_info
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
