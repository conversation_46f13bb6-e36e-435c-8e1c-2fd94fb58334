#!/bin/bash

# 启动本地 PostgreSQL Docker 服务
# 根据 application.yml 和 application-docker.yml 配置

echo "🚀 启动本地 PostgreSQL 服务..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker Desktop"
    exit 1
fi

# 停止并删除现有的 postgres 容器（如果存在）
echo "🧹 清理现有容器..."
docker stop postgres 2>/dev/null || true
docker rm postgres 2>/dev/null || true

# 创建数据卷（如果不存在）
docker volume create postgres_data 2>/dev/null || true

# 启动 PostgreSQL 容器
echo "📦 启动 PostgreSQL 容器..."
docker run -d \
  --name postgres \
  --restart always \
  -e POSTGRES_DB=testdb \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  -v "$(pwd)/init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh:ro" \
  postgres:15

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL 容器启动成功！"
    echo ""
    echo "📋 数据库连接信息："
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: testdb"
    echo "  用户名: postgres"
    echo "  密码: postgres"
    echo ""
    echo "🔍 检查容器状态："
    docker ps --filter name=postgres
    echo ""
    echo "📝 查看日志："
    echo "  docker logs postgres"
    echo ""
    echo "🛑 停止服务："
    echo "  docker stop postgres"
else
    echo "❌ PostgreSQL 容器启动失败"
    echo "📝 尝试查看错误日志："
    docker logs postgres 2>/dev/null || echo "无法获取日志"
    exit 1
fi
