#!/bin/bash

echo "🚀 启动简化的 AI Agent 服务栈..."
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -i:$port > /dev/null 2>&1; then
        log_warning "$service_name 端口 $port 已被占用"
        PID=$(lsof -ti:$port)
        log_info "正在停止进程 PID: $PID"
        kill -9 $PID 2>/dev/null
        sleep 2
        
        if lsof -i:$port > /dev/null 2>&1; then
            log_error "无法停止端口 $port 上的进程"
            return 1
        else
            log_success "已停止端口 $port 上的进程"
        fi
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 启动成功！"
            return 0
        fi
        
        if [ $((attempt % 5)) -eq 0 ]; then
            log_info "等待 $service_name 启动... ($attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 启动超时"
    return 1
}

# 1. 检查 PostgreSQL 数据库
log_info "检查 PostgreSQL 数据库状态..."
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin

if ! psql -h localhost -U sunyilin testdb -c "SELECT 1;" > /dev/null 2>&1; then
    log_warning "PostgreSQL 未运行，正在启动..."
    brew services start postgresql@15
    sleep 5
    
    if ! psql -h localhost -U sunyilin testdb -c "SELECT 1;" > /dev/null 2>&1; then
        log_error "PostgreSQL 启动失败"
        exit 1
    fi
fi
log_success "PostgreSQL 数据库运行正常"

# 2. 检查 Python 环境
log_info "检查 Python 环境..."
if ! python3 --version > /dev/null 2>&1; then
    log_error "未找到 Python3"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
log_success "Python 环境正常: $PYTHON_VERSION"

# 3. 停止现有服务
log_info "停止现有服务..."
check_port 8081 "MCP Server"
check_port 8080 "AI Agent Core"
check_port 8888 "Test Server"
check_port 8889 "Password Test Server"

# 4. 启动简单的 MCP 服务器
log_info "启动简单的 MCP 服务器..."
nohup python3 start-simple-mcp-server.py > mcp-server.log 2>&1 &
MCP_PID=$!
log_info "MCP 服务器启动中，PID: $MCP_PID"

# 等待 MCP 服务器启动
if ! wait_for_service "http://localhost:8081/mcp/health" "MCP Server"; then
    log_error "MCP 服务器启动失败，查看日志: tail -f mcp-server.log"
    exit 1
fi

# 5. 启动带密码认证的数据库测试服务器
log_info "启动数据库测试服务器..."
nohup python3 simple-test-server-with-password.py > db-test-server.log 2>&1 &
DB_TEST_PID=$!
log_info "数据库测试服务器启动中，PID: $DB_TEST_PID"

# 等待数据库测试服务器启动
if ! wait_for_service "http://localhost:8889/health" "Database Test Server"; then
    log_warning "数据库测试服务器启动失败，但继续启动其他服务"
fi

# 6. 测试服务连接
log_info "测试服务连接..."

# 测试 MCP 服务器
log_info "测试 MCP 工具列表..."
mcp_tools=$(curl -s -X POST http://localhost:8081/mcp \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}')

if [ $? -eq 0 ]; then
    log_success "MCP 服务器工具列表获取成功"
    echo "可用工具: $(echo $mcp_tools | jq -r '.result.tools[].name' 2>/dev/null | tr '\n' ', ' | sed 's/,$//')"
else
    log_warning "MCP 服务器工具列表获取失败"
fi

# 测试 MCP 工具调用
log_info "测试 MCP 工具调用..."
weather_result=$(curl -s -X POST http://localhost:8081/mcp \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"get_weather","arguments":{"city":"北京"}}}')

if [ $? -eq 0 ]; then
    log_success "MCP 工具调用测试成功"
else
    log_warning "MCP 工具调用测试失败"
fi

# 7. 服务启动完成
echo ""
echo "🎉 简化服务栈启动完成！"
echo "=================================="
echo ""
log_success "服务状态:"
echo "  📊 PostgreSQL 数据库: localhost:5432 (运行中)"
echo "  🔧 简单 MCP 服务器: http://localhost:8081 (PID: $MCP_PID)"
echo "  🗄️  数据库测试服务器: http://localhost:8889 (PID: $DB_TEST_PID)"
echo ""
echo "📋 服务端点:"
echo "  MCP 健康检查: http://localhost:8081/mcp/health"
echo "  MCP 工具列表: http://localhost:8081/mcp/tools"
echo "  数据库健康检查: http://localhost:8889/health"
echo "  数据库聊天接口: http://localhost:8889/chat"
echo ""
echo "📝 日志文件:"
echo "  MCP 服务器: tail -f mcp-server.log"
echo "  数据库测试: tail -f db-test-server.log"
echo ""
echo "🛑 停止服务:"
echo "  停止所有: kill $MCP_PID $DB_TEST_PID"
echo "  或使用: ./stop-all.sh"
echo ""
echo "🧪 测试命令:"
echo ""
echo "# 测试 MCP 工具列表"
echo "curl -X POST http://localhost:8081/mcp \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"tools/list\"}'"
echo ""
echo "# 测试天气工具"
echo "curl -X POST http://localhost:8081/mcp \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/call\",\"params\":{\"name\":\"get_weather\",\"arguments\":{\"city\":\"上海\"}}}'"
echo ""
echo "# 测试计算工具"
echo "curl -X POST http://localhost:8081/mcp \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"calculate\",\"arguments\":{\"expression\":\"10 + 5 * 2\"}}}'"
echo ""
echo "# 测试数据库聊天"
echo "curl -X POST http://localhost:8889/chat \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"message\":\"你好，测试MCP服务\",\"sessionId\":\"mcp-test\"}'"
