package com.example.aiagent.mcp.server;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class McpResponse {
    
    private String jsonrpc = "2.0";
    private String id;
    private Object result;
    private McpError error;
    
    public McpResponse() {}
    
    @Data
    public static class McpError {
        private int code;
        private String message;
        private Object data;
    }
}