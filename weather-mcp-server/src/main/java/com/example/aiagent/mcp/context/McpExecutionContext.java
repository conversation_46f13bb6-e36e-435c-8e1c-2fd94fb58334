package com.example.aiagent.mcp.context;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP执行上下文
 * 包含用户信息、权限信息等执行时需要的上下文数据
 */
@Data
public class McpExecutionContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 用户权限列表
     */
    private java.util.Set<String> permissions;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 客户端信息
     */
    private String clientInfo;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 请求时间戳
     */
    private long timestamp = System.currentTimeMillis();
    
    /**
     * 检查是否有指定权限
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    /**
     * 检查是否有任一权限
     */
    public boolean hasAnyPermission(String... requiredPermissions) {
        if (permissions == null || requiredPermissions == null) {
            return false;
        }
        
        for (String permission : requiredPermissions) {
            if (permissions.contains(permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有所有权限
     */
    public boolean hasAllPermissions(String... requiredPermissions) {
        if (permissions == null || requiredPermissions == null) {
            return false;
        }
        
        for (String permission : requiredPermissions) {
            if (!permissions.contains(permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 添加扩展属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    /**
     * 获取扩展属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
}
