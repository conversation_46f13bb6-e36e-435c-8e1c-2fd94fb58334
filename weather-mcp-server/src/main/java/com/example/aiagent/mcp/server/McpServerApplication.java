package com.example.aiagent.mcp.server;

import com.example.aiagent.mcp.context.McpExecutionContext;
import com.example.aiagent.mcp.registry.DynamicMcpServiceRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 通用MCP服务器应用
 * 支持动态注入Spring服务作为MCP工具
 */
@SpringBootApplication(scanBasePackages = "com.example.aiagent")
@RestController
public class McpServerApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServerApplication.class);
    
    @Autowired
    private DynamicMcpServiceRegistry serviceRegistry;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    public static void main(String[] args) {
        logger.info("🚀 启动通用MCP服务器...");
        
        ConfigurableApplicationContext context = SpringApplication.run(McpServerApplication.class, args);
        
        logger.info("✅ MCP服务器启动成功！");
        logger.info("🌐 MCP接口: http://localhost:8081/mcp");
        logger.info("📊 统计接口: http://localhost:8081/mcp/stats");
        logger.info("❤️ 健康检查: http://localhost:8081/mcp/health");
    }
    
    /**
     * 处理MCP请求
     */
    @PostMapping("/mcp")
    public Map<String, Object> handleMcpRequest(@RequestBody Map<String, Object> request,
                                               @RequestHeader(value = "X-User-ID", required = false) String userId,
                                               @RequestHeader(value = "X-User-Role", required = false) String userRole,
                                               @RequestHeader(value = "X-Session-ID", required = false) String sessionId) {
        
        String method = (String) request.get("method");
        Object idObj = request.get("id");
        String id = idObj != null ? idObj.toString() : null;
        
        logger.info("📨 收到MCP请求: {} (用户: {})", method, userId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("id", id);
        
        try {
            // 创建执行上下文
            McpExecutionContext context = createExecutionContext(userId, userRole, sessionId, id);
            
            switch (method) {
                case "initialize":
                    logger.info("处理 initialize 请求");
                    Map<String, Object> initResult = handleInitialize();
                    response.put("result", initResult);
                    break;
                    
                case "tools/list":
                    logger.info("处理 tools/list 请求");
                    Map<String, Object> toolsList = handleToolsList(context);
                    response.put("result", toolsList);
                    break;
                    
                case "tools/call":
                    logger.info("处理 tools/call 请求");
                    Map<String, Object> result = handleToolCall(request, context);
                    response.put("result", result);
                    break;
                    
                case "notifications/initialized":
                    logger.info("处理 initialized 通知");
                    response.put("result", null);
                    break;
                    
                case "ping":
                    logger.info("处理 ping 请求");
                    response.put("result", Map.of("status", "pong"));
                    break;
                    
                default:
                    logger.warn("未知的MCP方法: {}", method);
                    Map<String, Object> error = new HashMap<>();
                    error.put("code", -32601);
                    error.put("message", "Method not found: " + method);
                    response.put("error", error);
            }
            
        } catch (Exception e) {
            logger.error("❌ MCP请求处理失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("code", -32603);
            error.put("message", "Internal error: " + e.getMessage());
            response.put("error", error);
        }
        
        return response;
    }
    
    /**
     * 创建执行上下文
     */
    private McpExecutionContext createExecutionContext(String userId, String userRole, String sessionId, String requestId) {
        McpExecutionContext context = new McpExecutionContext();
        context.setUserId(userId != null ? userId : "anonymous");
        context.setRole(userRole != null ? userRole : "guest");
        context.setSessionId(sessionId != null ? sessionId : "session_" + System.currentTimeMillis());
        context.setRequestId(requestId);
        
        // 设置权限
        Set<String> permissions = new HashSet<>();
        if ("admin".equals(userRole)) {
            permissions.addAll(Arrays.asList("weather:read", "calc:use", "admin"));
        } else if ("user".equals(userRole)) {
            permissions.addAll(Arrays.asList("weather:read", "calc:use"));
        }
        // guest用户只有基本权限
        permissions.add("weather:read");
        permissions.add("calc:use");
        
        context.setPermissions(permissions);
        
        return context;
    }
    
    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize() {
        logger.info("处理MCP初始化请求");
        
        Map<String, Object> capabilities = new HashMap<>();
        
        // 服务器能力
        Map<String, Object> tools = new HashMap<>();
        tools.put("listChanged", false);
        capabilities.put("tools", tools);
        
        // 服务器信息
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "generic-mcp-server");
        serverInfo.put("version", "2.0.0");
        serverInfo.put("description", "Generic MCP Server with Dynamic Spring Service Integration");
        
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("capabilities", capabilities);
        result.put("serverInfo", serverInfo);
        
        return result;
    }
    
    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList(McpExecutionContext context) {
        logger.info("获取工具列表 (用户: {}, 权限: {})", context.getUserId(), context.getPermissions());
        
        // 根据用户权限获取可用工具
        var userTools = serviceRegistry.getToolDefinitionsForUser(context);
        
        Map<String, Object> result = new HashMap<>();
        result.put("tools", userTools);
        
        logger.info("✅ 返回 {} 个可用工具", userTools.size());
        return result;
    }
    
    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolCall(Map<String, Object> request, McpExecutionContext context) {
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        
        logger.info("🔧 调用工具: {} (用户: {}, 参数: {})", toolName, context.getUserId(), arguments);
        
        try {
            // 执行工具
            Object result = serviceRegistry.executeTool(toolName, arguments, context);
            
            // 构建MCP响应格式
            Map<String, Object> response = new HashMap<>();
            response.put("content", List.of(Map.of(
                "type", "text",
                "text", result
            )));
            response.put("isError", false);
            
            logger.info("✅ 工具调用成功: {}", toolName);
            return response;
            
        } catch (Exception e) {
            logger.error("❌ 工具调用失败: {} - {}", toolName, e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("content", List.of(Map.of(
                "type", "text",
                "text", "工具调用失败: " + e.getMessage()
            )));
            errorResponse.put("isError", true);
            
            return errorResponse;
        }
    }
    
    /**
     * 获取服务统计信息
     */
    @GetMapping("/mcp/stats")
    public Map<String, Object> getStats(@RequestHeader(value = "X-User-ID", required = false) String userId) {
        logger.info("📊 获取MCP服务统计 (用户: {})", userId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalTools", serviceRegistry.getAllToolDefinitions().size());
        stats.put("timestamp", System.currentTimeMillis());
        stats.put("serverVersion", "2.0.0");
        
        // 按服务分组统计
        Map<String, Integer> serviceStats = new HashMap<>();
        for (var tool : serviceRegistry.getAllToolDefinitions()) {
            String serviceName = tool.getFunction().getName().split("_")[0];
            serviceStats.put(serviceName, serviceStats.getOrDefault(serviceName, 0) + 1);
        }
        stats.put("serviceBreakdown", serviceStats);
        
        return stats;
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/mcp/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        health.put("timestamp", System.currentTimeMillis());
        health.put("availableTools", serviceRegistry.getAllToolDefinitions().size());
        
        return health;
    }
}
