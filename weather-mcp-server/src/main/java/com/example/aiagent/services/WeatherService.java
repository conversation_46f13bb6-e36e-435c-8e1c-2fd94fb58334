package com.example.aiagent.services;

import com.example.aiagent.mcp.annotation.McpService;
import com.example.aiagent.mcp.annotation.McpTool;
import com.example.aiagent.mcp.context.McpExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 天气服务 - 作为Spring Service注入到MCP服务器
 * 演示如何将业务服务集成到通用MCP服务器中
 */
@Service
@McpService(
    name = "weather",
    description = "天气查询服务，支持中文城市名查询",
    version = "2.0.0",
    requireAuth = false,
    permissions = {"weather:read"}
)
public class WeatherService {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherService.class);
    
    private long queryCount = 0;
    
    // 简单的城市名称映射
    private final Map<String, String> cityMapping = new HashMap<>();
    
    public WeatherService() {
        initializeCityMapping();
    }
    
    private void initializeCityMapping() {
        cityMapping.put("北京", "Beijing");
        cityMapping.put("上海", "Shanghai");
        cityMapping.put("广州", "Guangzhou");
        cityMapping.put("深圳", "Shenzhen");
        cityMapping.put("杭州", "Hangzhou");
        cityMapping.put("南京", "Nanjing");
        cityMapping.put("武汉", "Wuhan");
        cityMapping.put("成都", "Chengdu");
        cityMapping.put("西安", "Xian");
        cityMapping.put("天津", "Tianjin");
        cityMapping.put("重庆", "Chongqing");
        cityMapping.put("青岛", "Qingdao");
        cityMapping.put("大连", "Dalian");
        cityMapping.put("厦门", "Xiamen");
        cityMapping.put("苏州", "Suzhou");
        
        logger.info("初始化城市映射完成，支持 {} 个中文城市", cityMapping.size());
    }
    
    @McpTool(
        name = "get_weather",
        description = "获取指定城市的当前天气信息，支持中文城市名",
        requireAuth = false,
        example = "get_weather('北京') 或 get_weather('Beijing')"
    )
    public Map<String, Object> getWeather(String city, McpExecutionContext context) {
        logger.info("🌤️ 查询天气: {} (用户: {})", city, context.getUserId());
        
        try {
            // 城市名称映射
            String mappedCity = mapCityName(city);
            if (!city.equals(mappedCity)) {
                logger.info("🗺️ 城市名称映射: {} -> {}", city, mappedCity);
            }
            
            // 模拟天气数据
            Map<String, Object> weatherData = generateWeatherData(mappedCity);
            
            // 添加查询信息
            weatherData.put("originalCity", city);
            weatherData.put("mappedCity", mappedCity);
            weatherData.put("queryTime", System.currentTimeMillis());
            weatherData.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气查询成功: {}", city);
            return weatherData;
            
        } catch (Exception e) {
            logger.error("❌ 天气查询失败: {} - {}", city, e.getMessage(), e);
            throw new RuntimeException("天气查询失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "get_weather_forecast",
        description = "获取指定城市的天气预报（未来3天）",
        requireAuth = false,
        example = "get_weather_forecast('上海')"
    )
    public Map<String, Object> getWeatherForecast(String city, McpExecutionContext context) {
        logger.info("📅 查询天气预报: {} (用户: {})", city, context.getUserId());
        
        try {
            String mappedCity = mapCityName(city);
            
            Map<String, Object> forecast = new HashMap<>();
            forecast.put("city", mappedCity);
            forecast.put("originalCity", city);
            forecast.put("country", "CN");
            
            // 生成3天预报
            Map<String, Object>[] days = new Map[3];
            for (int i = 0; i < 3; i++) {
                Map<String, Object> dayForecast = new HashMap<>();
                dayForecast.put("date", "2024-08-" + String.format("%02d", i + 1));
                dayForecast.put("temperature", 25 + (Math.random() * 10));
                dayForecast.put("humidity", 60 + (Math.random() * 20));
                dayForecast.put("description", i == 0 ? "sunny" : (i == 1 ? "cloudy" : "rainy"));
                days[i] = dayForecast;
            }
            
            forecast.put("forecast", days);
            forecast.put("queryTime", System.currentTimeMillis());
            forecast.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气预报查询成功: {}", city);
            return forecast;
            
        } catch (Exception e) {
            logger.error("❌ 天气预报查询失败: {} - {}", city, e.getMessage(), e);
            throw new RuntimeException("天气预报查询失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "compare_weather",
        description = "比较两个城市的天气情况",
        requireAuth = false,
        example = "compare_weather('北京', '上海')"
    )
    public Map<String, Object> compareWeather(String city1, String city2, McpExecutionContext context) {
        logger.info("⚖️ 比较天气: {} vs {} (用户: {})", city1, city2, context.getUserId());
        
        try {
            Map<String, Object> weather1 = generateWeatherData(mapCityName(city1));
            Map<String, Object> weather2 = generateWeatherData(mapCityName(city2));
            
            Map<String, Object> comparison = new HashMap<>();
            comparison.put("city1", weather1);
            comparison.put("city2", weather2);
            
            // 添加比较结果
            double temp1 = (Double) weather1.get("temperature");
            double temp2 = (Double) weather2.get("temperature");
            
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("temperatureDifference", Math.abs(temp1 - temp2));
            analysis.put("warmerCity", temp1 > temp2 ? city1 : city2);
            analysis.put("recommendation", temp1 > temp2 ? 
                city1 + "比较温暖" : city2 + "比较温暖");
            
            comparison.put("analysis", analysis);
            comparison.put("queryTime", System.currentTimeMillis());
            comparison.put("queriedBy", context.getUserId());
            
            queryCount++;
            
            logger.info("✅ 天气比较完成: {} vs {}", city1, city2);
            return comparison;
            
        } catch (Exception e) {
            logger.error("❌ 天气比较失败: {} vs {} - {}", city1, city2, e.getMessage(), e);
            throw new RuntimeException("天气比较失败: " + e.getMessage(), e);
        }
    }
    
    @McpTool(
        name = "get_weather_stats",
        description = "获取天气服务统计信息",
        requireAuth = false
    )
    public Map<String, Object> getWeatherStats(McpExecutionContext context) {
        logger.info("📊 获取天气服务统计 (用户: {})", context.getUserId());
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalQueries", queryCount);
        stats.put("serviceStatus", "active");
        stats.put("supportedCities", cityMapping.size());
        stats.put("timestamp", System.currentTimeMillis());
        
        return stats;
    }
    
    @McpTool(
        name = "get_supported_cities",
        description = "获取支持的中文城市列表",
        requireAuth = false
    )
    public Map<String, Object> getSupportedCities(McpExecutionContext context) {
        logger.info("🏙️ 获取支持的城市列表 (用户: {})", context.getUserId());
        
        Map<String, Object> result = new HashMap<>();
        result.put("cities", cityMapping.keySet());
        result.put("count", cityMapping.size());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }
    
    /**
     * 城市名称映射
     */
    private String mapCityName(String city) {
        return cityMapping.getOrDefault(city, city);
    }
    
    /**
     * 生成模拟天气数据
     */
    private Map<String, Object> generateWeatherData(String city) {
        Map<String, Object> weatherData = new HashMap<>();
        weatherData.put("city", city);
        weatherData.put("country", "CN");
        weatherData.put("temperature", 20 + (Math.random() * 20)); // 20-40度
        weatherData.put("humidity", 40 + (Math.random() * 40)); // 40-80%
        weatherData.put("pressure", 1000 + (Math.random() * 50)); // 1000-1050 hPa
        weatherData.put("windSpeed", Math.random() * 10); // 0-10 m/s
        weatherData.put("visibility", 5000 + (Math.random() * 5000)); // 5-10km
        weatherData.put("description", Math.random() > 0.5 ? "sunny" : "cloudy");
        weatherData.put("feelsLike", (Double) weatherData.get("temperature") + (Math.random() * 5 - 2.5));
        weatherData.put("units", "metric");
        
        return weatherData;
    }
}
