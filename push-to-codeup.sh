#!/bin/bash

echo "🚀 准备推送AI Agent Framework到阿里云CodeUp..."

# 检查SSH连接
echo "🔑 测试SSH连接..."
ssh -T *********************

if [ $? -eq 0 ]; then
    echo "✅ SSH连接成功！"
    
    echo "📤 推送代码到远程仓库..."
    git push -u origin main
    
    if [ $? -eq 0 ]; then
        echo "🎉 代码推送成功！"
        echo "📋 仓库地址: https://codeup.aliyun.com/646198a1df66cc2d3fd12e96/ai-agent-framework.git"
        echo ""
        echo "🏗️ 项目包含："
        echo "  ✨ 认知架构 (Perception → Planning → Action → Reflection)"
        echo "  ⚡ Function Calling 模式 (快速执行)"
        echo "  🔄 ReAct 模式 (迭代推理)"
        echo "  🌊 流式输出支持"
        echo "  🌤️ 天气查询 (支持中文城市名)"
        echo "  🔧 清晰的模块化架构"
        echo ""
        echo "🎯 API端点："
        echo "  - /api/agent/chat (完整响应)"
        echo "  - /api/agent/chat/simple (仅内容)"
        echo "  - /api/agent/chat/flexible (可配置格式)"
        echo "  - /api/agent/chat/react-stream (流式ReAct)"
        echo "  - /api/agent/debug/tools (调试信息)"
    else
        echo "❌ 推送失败！请检查网络连接和权限。"
    fi
else
    echo "❌ SSH连接失败！"
    echo "请确保已在阿里云CodeUp中添加SSH公钥："
    echo ""
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDVlWjwgXBbuHEZSu2s/61wx1+8G3v7+CKzuJivfrXAOrVZekiEcVuswEz+aRE/qw9Hp+6GlDrlZuyBjkfg0v//RHXqAZFFgxYQCIF6VtqLbqhDo7lPcYz7k275V43pCP7TSDhycdMaEhHgigSWkKj75YWtW3M9qRrW9+9lrwuZlOqIXXCZ28yj1TKmsZJqigaE/JCh362YTGH8tOQHV6YQVLgyc+sz0J9vmdB/y2G8YwYtW7UX1806RD/0/q2j7fI0+Q/7mbXYJcL3Ath+VZOTRnD1tzOns/r0hP1QLaXlFOzck8uSkkvKAK8gKb5vTS6dOMSaxIyW77J8jqLyNJNsHSZCYt2nOpr3s9Fg/NokZG59doXdSAhhhXyp7AfBlXYRRiYGbKMTJMR/CGP7f/DlsKWBvlTOQcK/6JuoO0MZHLkSKRUb0Ru5APg0ko2dLuFNGdWBnROTFckjCgw1tbDRokTQGMOXzXY5HLt8e1AMcS+1UqCp3Lu6qj5ZcU5LulpmEp00g2jM2tspkjbrELogp3CsBIMqwL2pNCiEviO7XIOEcaeG8rWnRk1rg0b02fCd0svLOJIZFRb0bRYD6jCDx0EqUHNRdkZdg/QwloLCD16T1zv4LQ55wJgdIlaOjdpk0+81Yt8wtZz/KHxOcOI2ZlMVS9hwikzdCVkb3D+UTQ== yilin@ai-agent-framework"
    echo ""
    echo "添加步骤："
    echo "1. 访问 https://codeup.aliyun.com/"
    echo "2. 登录 → 个人设置 → SSH公钥"
    echo "3. 新增公钥 → 粘贴上面的公钥内容"
    echo "4. 保存后重新运行此脚本"
fi
