#!/bin/bash

echo "🔧 配置 Docker 镜像加速器..."

# 创建或更新 Docker daemon 配置
DOCKER_CONFIG_DIR="$HOME/.docker"
DAEMON_CONFIG="$DOCKER_CONFIG_DIR/daemon.json"

# 确保目录存在
mkdir -p "$DOCKER_CONFIG_DIR"

# 备份现有配置
if [ -f "$DAEMON_CONFIG" ]; then
    cp "$DAEMON_CONFIG" "$DAEMON_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
    echo "📋 已备份现有配置到 $DAEMON_CONFIG.backup.*"
fi

# 创建新的 daemon.json 配置
cat > "$DAEMON_CONFIG" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://registry.docker-cn.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}
EOF

echo "✅ Docker 镜像加速器配置完成"
echo "📋 配置文件位置: $DAEMON_CONFIG"
echo ""
echo "⚠️  请重启 Docker Desktop 以使配置生效："
echo "   1. 打开 Docker Desktop"
echo "   2. 点击右上角设置图标"
echo "   3. 重启 Docker Desktop"
echo ""
echo "🔄 或者使用命令行重启 Docker（如果支持）："
echo "   sudo systemctl restart docker  # Linux"
echo "   # macOS 需要通过 Docker Desktop 重启"
echo ""
echo "📝 配置内容："
cat "$DAEMON_CONFIG"
