#!/bin/bash

echo "🧪 测试本地PostgreSQL连接"
echo "========================="

# 数据库连接信息
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="testdb"
DB_USER="postgres"
DB_PASSWORD="postgres"

echo "📋 数据库连接信息:"
echo "  主机: $DB_HOST"
echo "  端口: $DB_PORT"
echo "  数据库: $DB_NAME"
echo "  用户名: $DB_USER"
echo "  密码: $DB_PASSWORD"

# 1. 检查PostgreSQL是否运行
echo ""
echo "🔍 检查PostgreSQL服务..."
if pg_isready -h $DB_HOST -p $DB_PORT > /dev/null 2>&1; then
    echo "✅ PostgreSQL服务正在运行"
else
    echo "❌ PostgreSQL服务未运行"
    echo "请启动PostgreSQL服务或Docker容器:"
    echo "  Docker: docker-compose up -d postgres"
    exit 1
fi

# 2. 测试数据库连接
echo ""
echo "🔗 测试数据库连接..."
if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败"
    echo "请检查数据库是否存在和用户权限"
    exit 1
fi

# 3. 检查数据库中是否存在对话历史表
echo ""
echo "📊 检查数据库表结构..."
TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'conversation_history');" | xargs)

if [ "$TABLE_EXISTS" = "t" ]; then
    echo "✅ conversation_history表已存在"
    
    # 查询表中的记录数
    RECORD_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM conversation_history;" | xargs)
    echo "📈 表中记录数: $RECORD_COUNT"
    
    # 显示最近的几条记录
    if [ "$RECORD_COUNT" -gt 0 ]; then
        echo ""
        echo "📋 最近的对话记录:"
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
            SELECT 
                id, 
                session_id, 
                user_id, 
                message_type, 
                LEFT(content, 50) as content_preview,
                created_at 
            FROM conversation_history 
            ORDER BY created_at DESC 
            LIMIT 5;
        "
    fi
else
    echo "⚠️ conversation_history表不存在"
    echo "表将在应用启动时通过Flyway自动创建"
fi

# 4. 检查Flyway迁移历史
echo ""
echo "🔄 检查Flyway迁移历史..."
FLYWAY_TABLE_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'flyway_schema_history');" | xargs)

if [ "$FLYWAY_TABLE_EXISTS" = "t" ]; then
    echo "✅ Flyway迁移表存在"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
        SELECT 
            version, 
            description, 
            type, 
            installed_on, 
            success 
        FROM flyway_schema_history 
        ORDER BY installed_rank;
    "
else
    echo "⚠️ Flyway迁移表不存在，将在首次启动时创建"
fi

# 5. 测试应用连接
echo ""
echo "🚀 测试应用连接..."
if curl -s http://localhost:8080/api/agent/debug/tools > /dev/null 2>&1; then
    echo "✅ AI Agent应用正在运行"
    
    # 测试对话历史API
    if curl -s http://localhost:8080/api/conversation/stats/system > /dev/null 2>&1; then
        echo "✅ 对话历史API正常"
        
        # 获取系统统计
        echo ""
        echo "📊 系统统计信息:"
        curl -s http://localhost:8080/api/conversation/stats/system | jq '.' 2>/dev/null || echo "无法解析JSON响应"
    else
        echo "⚠️ 对话历史API无响应"
    fi
else
    echo "⚠️ AI Agent应用未运行"
    echo "请启动应用: java -jar ai-agent-core/target/ai-agent-core-1.0.0.jar"
fi

echo ""
echo "🎯 测试总结:"
echo "============"
echo "✅ PostgreSQL服务: 正常"
echo "✅ 数据库连接: 正常"
if [ "$TABLE_EXISTS" = "t" ]; then
    echo "✅ 数据库表: 已创建 ($RECORD_COUNT 条记录)"
else
    echo "⚠️ 数据库表: 待创建"
fi

echo ""
echo "📋 下一步操作:"
echo "1. 如果表不存在，启动应用让Flyway自动创建表"
echo "2. 测试对话功能以验证历史记录功能"
echo "3. 使用API查询对话历史"

echo ""
echo "🔗 有用的命令:"
echo "  连接数据库: PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
echo "  查看表结构: \\d conversation_history"
echo "  查看记录: SELECT * FROM conversation_history ORDER BY created_at DESC LIMIT 10;"
