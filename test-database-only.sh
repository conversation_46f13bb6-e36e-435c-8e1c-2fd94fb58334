#!/bin/bash

echo "🧪 测试数据库连接和基本功能..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 1. 测试 PostgreSQL 连接
echo "🔍 测试 PostgreSQL 连接..."
if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status, version();" 2>/dev/null; then
    echo "✅ PostgreSQL 连接正常"
else
    echo "❌ PostgreSQL 连接失败，正在启动服务..."
    brew services start postgresql@15
    sleep 3
    
    if psql testdb -c "SELECT 'PostgreSQL 连接成功!' as status;" 2>/dev/null; then
        echo "✅ PostgreSQL 服务启动成功"
    else
        echo "❌ PostgreSQL 服务启动失败"
        exit 1
    fi
fi

# 2. 创建 AI Agent 相关的数据库表
echo ""
echo "🗄️ 创建 AI Agent 数据库表..."
psql testdb << 'EOF'
-- 删除现有表（如果存在）
DROP TABLE IF EXISTS conversation_history CASCADE;
DROP TABLE IF EXISTS agent_sessions CASCADE;

-- 创建会话表
CREATE TABLE agent_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 创建对话历史表
CREATE TABLE conversation_history (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    message_id VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'tool')),
    content TEXT NOT NULL,
    tool_calls JSONB,
    tool_call_id VARCHAR(100),
    cognitive_architecture VARCHAR(50),
    processing_mode VARCHAR(50),
    response_time_ms BIGINT,
    token_usage JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES agent_sessions(session_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX idx_conversation_history_created_at ON conversation_history(created_at);
CREATE INDEX idx_agent_sessions_session_id ON agent_sessions(session_id);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 agent_sessions 表创建触发器
CREATE TRIGGER update_agent_sessions_updated_at 
    BEFORE UPDATE ON agent_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

SELECT 'AI Agent 数据库表创建完成' as status;
EOF

if [ $? -eq 0 ]; then
    echo "✅ AI Agent 数据库表创建成功"
else
    echo "❌ 数据库表创建失败"
    exit 1
fi

# 3. 插入测试数据
echo ""
echo "📝 插入测试数据..."
psql testdb << 'EOF'
-- 插入测试会话
INSERT INTO agent_sessions (session_id, user_id, metadata) VALUES 
('test-session-001', 'user-123', '{"source": "test", "version": "1.0"}'),
('test-session-002', 'user-456', '{"source": "api", "version": "1.0"}');

-- 插入测试对话
INSERT INTO conversation_history (
    session_id, message_id, role, content, 
    cognitive_architecture, processing_mode, response_time_ms
) VALUES 
('test-session-001', 'msg-001', 'user', '你好，请介绍一下你自己', 'ReAct', 'standard', 150),
('test-session-001', 'msg-002', 'assistant', '你好！我是 AI Agent，一个基于认知架构的智能助手。我可以帮助您解决各种问题，包括回答问题、执行任务和提供建议。我使用 ReAct（推理-行动）架构来处理复杂的任务。', 'ReAct', 'standard', 1200),
('test-session-001', 'msg-003', 'user', '今天天气怎么样？', 'ReAct', 'standard', 100),
('test-session-001', 'msg-004', 'assistant', '我需要调用天气服务来获取当前天气信息。', 'ReAct', 'tool_calling', 800),
('test-session-002', 'msg-005', 'user', '帮我计算 2+2', 'FunctionCalling', 'standard', 80),
('test-session-002', 'msg-006', 'assistant', '2 + 2 = 4', 'FunctionCalling', 'standard', 200);

SELECT 'Test data inserted successfully' as status;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 测试数据插入成功"
else
    echo "❌ 测试数据插入失败"
    exit 1
fi

# 4. 测试数据查询
echo ""
echo "🔍 测试数据查询..."
echo "📊 会话统计："
psql testdb -c "
SELECT 
    COUNT(*) as total_sessions,
    COUNT(DISTINCT user_id) as unique_users
FROM agent_sessions;
"

echo ""
echo "📊 对话统计："
psql testdb -c "
SELECT 
    role,
    COUNT(*) as message_count,
    AVG(response_time_ms) as avg_response_time
FROM conversation_history 
WHERE response_time_ms IS NOT NULL
GROUP BY role
ORDER BY role;
"

echo ""
echo "📋 最近的对话："
psql testdb -c "
SELECT 
    ch.session_id,
    ch.role,
    LEFT(ch.content, 50) || '...' as content_preview,
    ch.cognitive_architecture,
    ch.response_time_ms,
    ch.created_at
FROM conversation_history ch
ORDER BY ch.created_at DESC
LIMIT 10;
"

# 5. 测试复杂查询
echo ""
echo "🔍 测试复杂查询..."
echo "📊 按会话分组的对话统计："
psql testdb -c "
SELECT 
    s.session_id,
    s.user_id,
    COUNT(ch.id) as message_count,
    MIN(ch.created_at) as first_message,
    MAX(ch.created_at) as last_message,
    AVG(ch.response_time_ms) as avg_response_time
FROM agent_sessions s
LEFT JOIN conversation_history ch ON s.session_id = ch.session_id
GROUP BY s.session_id, s.user_id
ORDER BY s.created_at DESC;
"

# 6. 测试 JSON 查询
echo ""
echo "🔍 测试 JSON 查询..."
psql testdb -c "
SELECT 
    session_id,
    metadata->>'source' as source,
    metadata->>'version' as version
FROM agent_sessions
WHERE metadata->>'source' IS NOT NULL;
"

# 7. 性能测试
echo ""
echo "⚡ 简单性能测试..."
echo "插入 1000 条测试记录..."

# 生成大量测试数据
psql testdb << 'EOF'
-- 插入大量测试数据
INSERT INTO agent_sessions (session_id, user_id, metadata)
SELECT 
    'perf-session-' || generate_series,
    'perf-user-' || (generate_series % 100),
    '{"source": "performance_test", "batch": 1}'
FROM generate_series(1, 100);

INSERT INTO conversation_history (
    session_id, message_id, role, content, 
    cognitive_architecture, processing_mode, response_time_ms
)
SELECT 
    'perf-session-' || (generate_series % 100 + 1),
    'perf-msg-' || generate_series,
    CASE WHEN generate_series % 2 = 0 THEN 'user' ELSE 'assistant' END,
    'Performance test message ' || generate_series,
    CASE WHEN generate_series % 3 = 0 THEN 'ReAct' ELSE 'FunctionCalling' END,
    'standard',
    (random() * 2000)::int
FROM generate_series(1, 1000);

SELECT 'Performance test data inserted' as status;
EOF

echo "✅ 性能测试数据插入完成"

# 测试查询性能
echo "🔍 测试查询性能..."
time psql testdb -c "
SELECT 
    COUNT(*) as total_messages,
    AVG(response_time_ms) as avg_response_time,
    MIN(response_time_ms) as min_response_time,
    MAX(response_time_ms) as max_response_time
FROM conversation_history;
" > /dev/null

echo "✅ 查询性能测试完成"

# 8. 清理性能测试数据
echo ""
echo "🧹 清理性能测试数据..."
psql testdb -c "
DELETE FROM conversation_history WHERE message_id LIKE 'perf-msg-%';
DELETE FROM agent_sessions WHERE session_id LIKE 'perf-session-%';
SELECT 'Performance test data cleaned' as status;
"

# 9. 最终状态检查
echo ""
echo "📊 最终数据库状态："
psql testdb -c "
SELECT 
    'agent_sessions' as table_name,
    COUNT(*) as record_count
FROM agent_sessions
UNION ALL
SELECT 
    'conversation_history' as table_name,
    COUNT(*) as record_count
FROM conversation_history
ORDER BY table_name;
"

echo ""
echo "🎉 数据库测试完成！"
echo ""
echo "📋 测试结果总结："
echo "  ✅ PostgreSQL 连接正常"
echo "  ✅ AI Agent 数据库表创建成功"
echo "  ✅ 测试数据插入成功"
echo "  ✅ 基本查询功能正常"
echo "  ✅ 复杂查询功能正常"
echo "  ✅ JSON 查询功能正常"
echo "  ✅ 性能测试通过"
echo ""
echo "🔧 数据库管理命令："
echo "  连接数据库: psql testdb"
echo "  查看表结构: \\d+ table_name"
echo "  查看所有表: \\dt"
echo "  退出: \\q"
echo ""
echo "📝 示例查询："
echo "  查看会话: SELECT * FROM agent_sessions;"
echo "  查看对话: SELECT * FROM conversation_history ORDER BY created_at DESC LIMIT 10;"
echo "  统计信息: SELECT role, COUNT(*) FROM conversation_history GROUP BY role;"
echo ""
echo "💡 数据库已准备就绪，可以支持 AI Agent 应用！"
