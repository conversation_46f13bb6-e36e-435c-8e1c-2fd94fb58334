# AI Agent 架构文档

## 🏗️ 清晰的项目结构

```
ai-agent-core/
├── src/main/java/com/example/aiagent/
│   ├── 🚀 AiAgentApplication.java                    # Spring Boot 启动类
│   │
│   ├── 🧠 cognitive/                                 # 认知架构模块
│   │   ├── CognitiveArchitecture.java               # 认知架构接口
│   │   ├── BaseCognitiveArchitecture.java           # 基础认知架构实现
│   │   ├── CognitiveContext.java                    # 认知上下文
│   │   │
│   │   ├── components/                               # 认知组件
│   │   │   ├── PerceptionComponent.java             # 感知组件接口
│   │   │   ├── PlanningComponent.java               # 规划组件接口
│   │   │   ├── ActionComponent.java                 # 行动组件接口
│   │   │   └── ReflectionComponent.java             # 反思组件接口
│   │   │
│   │   └── impl/                                     # 具体实现
│   │       ├── FunctionCallingArchitecture.java    # Function Calling架构
│   │       ├── ReActArchitecture.java               # ReAct架构
│   │       └── components/fc/                       # Function Calling组件
│   │           ├── FCPerceptionComponent.java       # FC感知组件
│   │           ├── FCPlanningComponent.java         # FC规划组件
│   │           ├── FCActionComponent.java           # FC行动组件
│   │           └── FCReflectionComponent.java       # FC反思组件
│   │
│   ├── 🔧 core/                                      # 核心执行管道
│   │   ├── ExecutionPipeline.java                   # 执行管道
│   │   ├── RequestPreprocessor.java                 # 请求预处理器
│   │   └── ResponsePostprocessor.java               # 响应后处理器
│   │
│   ├── 🌐 controller/                                # Web控制器
│   │   └── AgentController.java                     # Agent API控制器
│   │
│   ├── 🛠️ service/                                   # 业务服务
│   │   ├── AgentService.java                        # Agent服务(简化版)
│   │   ├── AiModelService.java                      # AI模型服务接口
│   │   ├── CityNameMappingService.java              # 城市名称映射服务
│   │   └── impl/                                     # 服务实现
│   │       └── AlibabaAiModelService.java           # 阿里云AI模型实现
│   │
│   ├── 🔌 function/                                  # 函数执行模块
│   │   ├── FunctionExecutor.java                    # 函数执行器
│   │   ├── FunctionRegistry.java                    # 函数注册表
│   │   └── FunctionResult.java                      # 函数执行结果
│   │
│   ├── 🔗 mcp/                                       # MCP协议模块
│   │   ├── McpClient.java                           # MCP客户端
│   │   ├── McpFunctionAdapter.java                  # MCP函数适配器
│   │   ├── WeatherMcpService.java                   # 天气MCP服务
│   │   └── model/                                    # MCP数据模型
│   │
│   ├── 📊 model/                                     # 数据模型
│   │   ├── AgentMode.java                           # Agent模式枚举
│   │   ├── AgentRequest.java                        # Agent请求
│   │   ├── AgentResponse.java                       # Agent响应
│   │   ├── ChatMessage.java                         # 聊天消息
│   │   ├── ChatRequest.java                         # 聊天请求
│   │   ├── ChatResponse.java                        # 聊天响应
│   │   ├── ToolCall.java                            # 工具调用
│   │   └── ToolDefinition.java                      # 工具定义
│   │
│   ├── ⚙️ config/                                    # 配置模块
│   │   ├── AiModelConfig.java                       # AI模型配置
│   │   └── AiModelProperties.java                   # AI模型属性
│   │
│   └── ❌ exception/                                 # 异常处理
│       └── AiModelException.java                    # AI模型异常
│
└── 📋 test-react-stream.sh                          # 流式测试脚本
```

## 🧠 认知架构流程

### Function Calling 架构
```
感知 → 规划 → 行动 → 反思
  ↓      ↓      ↓      ↓
理解意图 → 选择工具 → 执行函数 → 生成答案
```

### ReAct 架构
```
感知 → [规划 → 行动 → 中间反思] × N轮 → 最终反思
  ↓         ↓      ↓        ↓           ↓
理解意图 → 制定计划 → 执行行动 → 评估进展 → 生成答案
```

## 🔄 执行管道流程

```
用户请求 → RequestPreprocessor → ExecutionPipeline → ResponsePostprocessor → 返回结果
              ↓                        ↓                      ↓
           验证标准化              选择认知架构              统计增强
                                      ↓
                              CognitiveArchitecture
                                      ↓
                            Perception → Planning → Action → Reflection
```

## 🎯 设计原则

1. **单一职责**: 每个类只负责一个明确的功能
2. **开闭原则**: 对扩展开放，对修改封闭
3. **依赖倒置**: 依赖抽象而不是具体实现
4. **接口隔离**: 使用小而专一的接口
5. **组合优于继承**: 通过组合实现功能复用

## 🚀 扩展指南

### 添加新的认知架构
1. 实现 `CognitiveArchitecture` 接口
2. 继承 `BaseCognitiveArchitecture` 类
3. 实现特定的认知组件
4. 在Spring容器中注册

### 添加新的认知组件
1. 实现对应的组件接口 (Perception/Planning/Action/Reflection)
2. 在具体架构中使用新组件
3. 通过依赖注入管理组件

### 添加新的函数
1. 创建MCP服务器
2. 实现 `McpFunctionAdapter`
3. 在 `FunctionRegistry` 中注册
4. 配置工具定义

## 📈 性能优化

1. **缓存机制**: 缓存常用的工具定义和结果
2. **异步处理**: 使用异步方式处理长时间任务
3. **连接池**: 复用HTTP连接和数据库连接
4. **流式处理**: 对于复杂任务使用流式输出
5. **监控指标**: 添加性能监控和日志记录
