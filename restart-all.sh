#!/bin/bash

# 一键重启所有服务
echo "=== 重启所有服务 ==="

# 停止所有相关服务
echo "停止所有AI Agent相关服务..."
pkill -f "java.*ai-agent-framework" 2>/dev/null || true
sleep 3

# 检查端口是否释放
echo "检查端口状态..."
if lsof -i:8080 > /dev/null 2>&1; then
    echo "强制释放端口8080..."
    kill -9 $(lsof -ti:8080) 2>/dev/null || true
fi

if lsof -i:8081 > /dev/null 2>&1; then
    echo "强制释放端口8081..."
    kill -9 $(lsof -ti:8081) 2>/dev/null || true
fi

sleep 2

# 重新构建项目
echo "重新构建项目..."
mvn clean package -DskipTests

# 启动天气MCP服务器
echo "启动天气MCP服务器..."
./start-weather-server.sh &
WEATHER_PID=$!

# 等待天气服务器启动
echo "等待天气MCP服务器启动..."
sleep 10  # 增加到 10 秒

# 检查天气服务器是否启动成功
if ! lsof -i:8081 > /dev/null 2>&1; then
    echo "错误：天气MCP服务器启动失败"
    exit 1
fi

# 额外检查：测试 MCP 服务器是否响应
echo "测试天气MCP服务器连接..."
for i in {1..10}; do
    if curl -s http://localhost:8081/health > /dev/null 2>&1; then
        echo "天气MCP服务器连接成功"
        break
    fi
    echo "等待天气MCP服务器响应... ($i/10)"
    sleep 2
done

echo "天气MCP服务器启动成功"

# 启动Agent服务
echo "启动Agent服务..."
./start-agent.sh &
AGENT_PID=$!

echo "=== 服务启动完成 ==="
echo "天气MCP服务器: http://localhost:8081"
echo "Agent服务: http://localhost:8080"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait