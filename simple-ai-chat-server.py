#!/usr/bin/env python3
"""
简单的 AI 聊天服务器
用于测试数据库连接和基本对话功能
"""

import json
import time
import uuid
from datetime import datetime
from flask import Flask, request, jsonify
import psycopg2
from psycopg2.extras import RealDictCursor
import requests

app = Flask(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'testdb',
    'user': 'sunyilin',
    'password': ''
}

# AI 模型配置（使用您的阿里云配置）
AI_CONFIG = {
    'api_key': 'sk-fc81d268ace64286a752e8ef084144cc',
    'endpoint': 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
    'model': 'qwen-plus'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def create_session(session_id, user_id=None):
    """创建新会话"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO agent_sessions (session_id, user_id, metadata)
                VALUES (%s, %s, %s)
                ON CONFLICT (session_id) DO NOTHING
            """, (session_id, user_id or 'anonymous', json.dumps({'source': 'python_test'})))
        conn.commit()
        return True
    except Exception as e:
        print(f"创建会话失败: {e}")
        return False
    finally:
        conn.close()

def save_message(session_id, message_id, role, content, response_time_ms=None, error_message=None):
    """保存消息到数据库"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO conversation_history (
                    session_id, message_id, role, content, 
                    cognitive_architecture, processing_mode, response_time_ms, error_message
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                session_id, message_id, role, content,
                'SimpleChat', 'standard', response_time_ms, error_message
            ))
        conn.commit()
        return True
    except Exception as e:
        print(f"保存消息失败: {e}")
        return False
    finally:
        conn.close()

def get_conversation_history(session_id, limit=10):
    """获取对话历史"""
    conn = get_db_connection()
    if not conn:
        return []
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT role, content, created_at, response_time_ms
                FROM conversation_history
                WHERE session_id = %s
                ORDER BY created_at ASC
                LIMIT %s
            """, (session_id, limit))
            return cur.fetchall()
    except Exception as e:
        print(f"获取对话历史失败: {e}")
        return []
    finally:
        conn.close()

def call_ai_model(messages):
    """调用 AI 模型"""
    try:
        headers = {
            'Authorization': f'Bearer {AI_CONFIG["api_key"]}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': AI_CONFIG['model'],
            'messages': messages,
            'max_tokens': 1000,
            'temperature': 0.7
        }
        
        response = requests.post(
            AI_CONFIG['endpoint'],
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
        
        return f"AI 服务调用失败: {response.status_code}"
        
    except Exception as e:
        return f"AI 服务调用异常: {str(e)}"

def generate_simple_response(message):
    """生成简单的响应（备用方案）"""
    message_lower = message.lower()
    
    if '你好' in message or 'hello' in message_lower:
        return "你好！我是 AI Agent 测试服务器。我可以帮助您测试对话功能。"
    elif '天气' in message:
        return "抱歉，我目前无法获取实时天气信息。这是一个测试服务器，主要用于验证数据库和基本对话功能。"
    elif '时间' in message:
        return f"当前时间是：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    elif '测试' in message:
        return "测试功能正常！数据库连接正常，消息已成功保存。"
    elif '介绍' in message or '你是谁' in message:
        return "我是一个简单的 AI 聊天测试服务器，基于 Python Flask 构建。我可以：\n1. 接收和响应消息\n2. 将对话保存到 PostgreSQL 数据库\n3. 调用阿里云通义千问 API（如果网络允许）\n4. 提供基本的对话功能测试"
    else:
        return f"我收到了您的消息：「{message}」。这是一个测试响应，表明系统运行正常。"

@app.route('/')
def home():
    """首页"""
    return jsonify({
        'status': 'success',
        'message': 'AI Chat Test Server is running!',
        'timestamp': datetime.now().isoformat(),
        'endpoints': {
            'chat': '/chat',
            'health': '/health',
            'history': '/history/<session_id>',
            'stats': '/stats'
        }
    })

@app.route('/health')
def health():
    """健康检查"""
    # 测试数据库连接
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                cur.execute("SELECT version()")
                db_version = cur.fetchone()[0]
            conn.close()
            db_status = "Connected"
        except Exception as e:
            db_status = f"Error: {str(e)}"
    else:
        db_status = "Disconnected"
    
    return jsonify({
        'status': 'UP' if conn else 'DOWN',
        'database': db_status,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/chat', methods=['POST'])
def chat():
    """聊天接口"""
    start_time = time.time()
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No JSON data provided'}), 400
        
        message = data.get('message', '').strip()
        session_id = data.get('sessionId', f'session_{uuid.uuid4().hex[:8]}')
        
        if not message:
            return jsonify({'success': False, 'error': 'Message cannot be empty'}), 400
        
        # 创建会话（如果不存在）
        create_session(session_id)
        
        # 保存用户消息
        user_msg_id = f'msg_{uuid.uuid4().hex[:8]}'
        save_message(session_id, user_msg_id, 'user', message)
        
        # 获取对话历史
        history = get_conversation_history(session_id, 5)
        
        # 构建消息上下文
        messages = []
        for h in history[-4:]:  # 最近4条消息作为上下文
            messages.append({
                'role': h['role'],
                'content': h['content']
            })
        
        # 添加当前用户消息
        messages.append({'role': 'user', 'content': message})
        
        # 生成响应
        try:
            # 首先尝试调用 AI 模型
            ai_response = call_ai_model(messages)
            if ai_response.startswith('AI 服务调用'):
                # AI 服务失败，使用简单响应
                response_content = generate_simple_response(message)
                response_content += f"\n\n注意：{ai_response}"
            else:
                response_content = ai_response
        except Exception as e:
            # 完全失败，使用简单响应
            response_content = generate_simple_response(message)
            response_content += f"\n\n注意：AI 服务异常 - {str(e)}"
        
        # 计算响应时间
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # 保存助手响应
        assistant_msg_id = f'msg_{uuid.uuid4().hex[:8]}'
        save_message(session_id, assistant_msg_id, 'assistant', response_content, response_time_ms)
        
        return jsonify({
            'success': True,
            'response': response_content,
            'sessionId': session_id,
            'responseTime': response_time_ms,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        error_msg = f"处理请求时发生错误: {str(e)}"
        return jsonify({'success': False, 'error': error_msg}), 500

@app.route('/history/<session_id>')
def get_history(session_id):
    """获取对话历史"""
    try:
        history = get_conversation_history(session_id, 20)
        return jsonify({
            'success': True,
            'sessionId': session_id,
            'history': [dict(h) for h in history],
            'count': len(history)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/stats')
def get_stats():
    """获取统计信息"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'error': 'Database connection failed'}), 500
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # 会话统计
            cur.execute("SELECT COUNT(*) as total_sessions FROM agent_sessions")
            session_stats = cur.fetchone()
            
            # 消息统计
            cur.execute("""
                SELECT 
                    role,
                    COUNT(*) as count,
                    AVG(response_time_ms) as avg_response_time
                FROM conversation_history 
                WHERE response_time_ms IS NOT NULL
                GROUP BY role
            """)
            message_stats = cur.fetchall()
            
            # 最近活动
            cur.execute("""
                SELECT session_id, COUNT(*) as message_count, MAX(created_at) as last_activity
                FROM conversation_history
                GROUP BY session_id
                ORDER BY last_activity DESC
                LIMIT 5
            """)
            recent_activity = cur.fetchall()
        
        return jsonify({
            'success': True,
            'stats': {
                'sessions': dict(session_stats),
                'messages': [dict(m) for m in message_stats],
                'recent_activity': [dict(r) for r in recent_activity]
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 启动 AI 聊天测试服务器...")
    print("📋 服务信息:")
    print("  端口: 5000")
    print("  数据库: PostgreSQL (testdb)")
    print("  AI 模型: 阿里云通义千问")
    print("")
    print("🧪 测试 URL:")
    print("  健康检查: http://localhost:5000/health")
    print("  发送消息: curl -X POST http://localhost:5000/chat -H 'Content-Type: application/json' -d '{\"message\":\"你好\",\"sessionId\":\"test\"}'")
    print("  查看历史: http://localhost:5000/history/test")
    print("  查看统计: http://localhost:5000/stats")
    print("")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
