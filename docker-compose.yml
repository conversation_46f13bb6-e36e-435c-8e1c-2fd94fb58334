version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: postgres
    restart: always
    environment:
      POSTGRES_DB: testdb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      # 额外创建 testuser 用户和 testdb 数据库
      POSTGRES_MULTIPLE_DATABASES: testdb
      # 通过 init 脚本创建 testuser
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d testdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai-agent-network

  # MCP服务器
  mcp-server:
    build:
      context: ./weather-mcp-server
      dockerfile: Dockerfile
    container_name: mcp-server
    restart: always
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/mcp/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-agent-network

  # AI Agent核心服务
  ai-agent-core:
    build:
      context: ./ai-agent-core
      dockerfile: Dockerfile
    container_name: ai-agent-core
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - MCP_SERVER_URL=http://mcp-server:8081/mcp
    depends_on:
      postgres:
        condition: service_healthy
      mcp-server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/agent/debug/tools"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai-agent-network

volumes:
  postgres_data:
    driver: local

networks:
  ai-agent-network:
    driver: bridge
