#!/bin/bash

# 启动AI Agent服务
echo "启动AI Agent服务..."

# 停止现有的Agent服务进程
echo "检查并停止现有的Agent服务..."
PID_8080=$(lsof -ti:8080)
if [ ! -z "$PID_8080" ]; then
    echo "发现端口8080上的进程 (PID: $PID_8080)，正在停止..."
    kill -9 $PID_8080
    sleep 2
    echo "已停止端口8080上的服务"
else
    echo "端口8080没有运行的服务"
fi

# 检查JAR文件
JAR_FILE="target/ai-agent-framework-1.0.0.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "JAR文件不存在，正在构建..."
    mvn clean package -DskipTests
fi

# 检查天气MCP服务器是否运行
echo "检查天气MCP服务器状态..."
if ! lsof -i:8081 > /dev/null 2>&1; then
    echo "警告：天气MCP服务器(端口8081)未运行，请先启动天气MCP服务器"
    echo "运行命令：./start-weather-server.sh"
    read -p "是否继续启动Agent服务？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "已取消启动"
        exit 1
    fi
else
    echo "天气MCP服务器正在运行"
fi

# 启动主Agent服务
echo "正在启动AI Agent服务 (端口8080)..."
java -jar "$JAR_FILE" \
  --server.port=8080 \
  --spring.application.name=ai-agent-framework