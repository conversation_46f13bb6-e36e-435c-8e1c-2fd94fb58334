#!/bin/bash

# 停止所有AI Agent相关服务
echo "=== 停止所有AI Agent服务 ==="

# 显示当前运行的Java进程
echo "当前运行的Java进程:"
ps aux | grep java | grep -v grep

# 停止ai-agent-core服务 (端口8080)
echo "\n停止ai-agent-core服务..."
if lsof -i:8080 > /dev/null 2>&1; then
    echo "发现端口8080上的服务，正在停止..."
    # 优雅停止
    pkill -f "ai-agent-core" 2>/dev/null || true
    sleep 3
    
    # 如果还在运行，强制停止
    if lsof -i:8080 > /dev/null 2>&1; then
        echo "优雅停止失败，强制停止端口8080服务..."
        kill -9 $(lsof -ti:8080) 2>/dev/null || true
    fi
    echo "ai-agent-core服务已停止"
else
    echo "ai-agent-core服务未运行"
fi

# 停止weather-mcp-server服务 (端口8081)
echo "\n停止weather-mcp-server服务..."
if lsof -i:8081 > /dev/null 2>&1; then
    echo "发现端口8081上的服务，正在停止..."
    # 优雅停止
    pkill -f "weather-mcp-server" 2>/dev/null || true
    sleep 3
    
    # 如果还在运行，强制停止
    if lsof -i:8081 > /dev/null 2>&1; then
        echo "优雅停止失败，强制停止端口8081服务..."
        kill -9 $(lsof -ti:8081) 2>/dev/null || true
    fi
    echo "weather-mcp-server服务已停止"
else
    echo "weather-mcp-server服务未运行"
fi

# 停止所有相关Java进程
echo "\n停止所有AI Agent相关Java进程..."
pkill -f "ai-agent" 2>/dev/null || true
pkill -f "weather.*mcp" 2>/dev/null || true
sleep 2

# 最终检查
echo "\n最终状态检查:"
echo "端口8080状态:"
if lsof -i:8080 > /dev/null 2>&1; then
    echo "  ❌ 端口8080仍被占用"
    lsof -i:8080
else
    echo "  ✅ 端口8080已释放"
fi

echo "端口8081状态:"
if lsof -i:8081 > /dev/null 2>&1; then
    echo "  ❌ 端口8081仍被占用"
    lsof -i:8081
else
    echo "  ✅ 端口8081已释放"
fi

echo "\n剩余Java进程:"
JAVA_PROCESSES=$(ps aux | grep java | grep -v grep | wc -l)
if [ $JAVA_PROCESSES -eq 0 ]; then
    echo "  ✅ 没有Java进程在运行"
else
    echo "  ⚠️  仍有 $JAVA_PROCESSES 个Java进程在运行:"
    ps aux | grep java | grep -v grep
fi

echo "\n=== 所有服务停止完成 ==="