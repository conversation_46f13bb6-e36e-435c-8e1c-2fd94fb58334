#!/bin/bash
set -e

# 这个脚本会在PostgreSQL容器启动时自动执行
# 用于创建AI Agent所需的数据库结构

echo "🚀 初始化AI Agent数据库..."

# 连接到testdb数据库并创建必要的扩展
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "testdb" <<-EOSQL
    -- 创建UUID扩展（如果需要）
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    -- 创建时间戳函数
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS \$\$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    \$\$ language 'plpgsql';
    
    -- 输出初始化完成信息
    SELECT 'AI Agent数据库初始化完成' as status;
EOSQL

echo "✅ AI Agent数据库初始化完成"
echo "📋 数据库信息:"
echo "  数据库名: testdb"
echo "  用户名: postgres"
echo "  密码: postgres"
echo "  端口: 5432"
