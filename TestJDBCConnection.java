import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class TestJDBCConnection {
    public static void main(String[] args) {
        String url = "***************************************";
        String username = "sunyilin";
        String password = "sunyilin";
        
        System.out.println("🔍 测试 PostgreSQL JDBC 连接...");
        System.out.println("URL: " + url);
        System.out.println("用户名: " + username);
        System.out.println("密码: " + password);
        System.out.println("================================");
        
        try {
            // 加载 PostgreSQL JDBC 驱动
            Class.forName("org.postgresql.Driver");
            System.out.println("✅ PostgreSQL JDBC 驱动加载成功");
            
            // 建立连接
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 数据库连接成功！");
            
            // 执行测试查询
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT version(), current_database(), current_user");
            
            if (resultSet.next()) {
                System.out.println("📊 数据库信息:");
                System.out.println("  版本: " + resultSet.getString(1).substring(0, 50) + "...");
                System.out.println("  数据库: " + resultSet.getString(2));
                System.out.println("  用户: " + resultSet.getString(3));
            }
            
            // 测试表查询
            ResultSet tables = statement.executeQuery(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
            );
            
            System.out.println("📋 可用的表:");
            while (tables.next()) {
                System.out.println("  - " + tables.getString(1));
            }
            
            // 关闭连接
            resultSet.close();
            statement.close();
            connection.close();
            
            System.out.println("✅ 连接测试完成，所有功能正常！");
            System.out.println("================================");
            System.out.println("🎯 IDEA 配置建议:");
            System.out.println("  使用相同的连接参数应该可以在 IDEA 中正常工作");
            
        } catch (Exception e) {
            System.out.println("❌ 连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
