#!/bin/bash

echo "🚀 启动 AI Agent 服务并测试对话功能..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 检查 PostgreSQL 服务状态
echo "🔍 检查 PostgreSQL 服务状态..."
if ! psql testdb -c "SELECT 'PostgreSQL 运行正常' as status;" >/dev/null 2>&1; then
    echo "⚠️  PostgreSQL 服务未运行，正在启动..."
    brew services start postgresql@15
    sleep 3
    
    if ! psql testdb -c "SELECT 'PostgreSQL 运行正常' as status;" >/dev/null 2>&1; then
        echo "❌ PostgreSQL 启动失败，请检查服务状态"
        exit 1
    fi
fi

echo "✅ PostgreSQL 服务运行正常"

# 检查 Docker 是否运行
echo "🔍 检查 Docker 服务状态..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker Desktop"
    exit 1
fi

echo "✅ Docker 服务运行正常"

# 停止现有容器
echo "🧹 清理现有容器..."
docker-compose down

# 构建并启动服务
echo "🏗️  构建并启动 AI Agent 服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 等待 AI Agent 服务完全启动
echo "⏳ 等待 AI Agent 服务完全启动..."
for i in {1..30}; do
    if curl -s http://localhost:8080/api/agent/debug/tools >/dev/null 2>&1; then
        echo "✅ AI Agent 服务启动成功！"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ AI Agent 服务启动超时"
        echo "📝 查看日志："
        docker-compose logs ai-agent-core
        exit 1
    fi
    
    echo "等待中... ($i/30)"
    sleep 2
done

# 显示服务信息
echo ""
echo "🎉 服务启动完成！"
echo ""
echo "📋 服务信息："
echo "  AI Agent API: http://localhost:8080"
echo "  MCP Server: http://localhost:8081"
echo "  PostgreSQL: localhost:5432"
echo ""

# 测试 AI 对话功能
echo "🤖 测试 AI 对话功能..."
echo ""

# 测试简单对话
echo "📝 发送测试消息: '你好，请介绍一下你自己'"
response=$(curl -s -X POST http://localhost:8080/api/agent/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下你自己",
    "conversationId": "test-conversation-1"
  }')

if [ $? -eq 0 ] && [ -n "$response" ]; then
    echo "✅ AI 对话测试成功！"
    echo ""
    echo "🤖 AI 回复："
    echo "$response" | jq -r '.response // .message // .' 2>/dev/null || echo "$response"
    echo ""
else
    echo "❌ AI 对话测试失败"
    echo "📝 查看 AI Agent 日志："
    docker-compose logs --tail=20 ai-agent-core
fi

echo ""
echo "🔧 管理命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  查看状态: docker-compose ps"
echo ""
echo "🧪 测试命令："
echo "  测试工具: curl http://localhost:8080/api/agent/debug/tools"
echo "  健康检查: curl http://localhost:8080/actuator/health"
echo "  发送消息: curl -X POST http://localhost:8080/api/agent/chat -H 'Content-Type: application/json' -d '{\"message\":\"你好\",\"conversationId\":\"test\"}'"
