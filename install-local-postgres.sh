#!/bin/bash

echo "🚀 安装本地 PostgreSQL 服务（使用 Homebrew）..."

# 检查是否安装了 Homebrew
if ! command -v brew &> /dev/null; then
    echo "❌ 未找到 Homebrew，请先安装 Homebrew："
    echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

echo "📦 更新 Homebrew..."
brew update

echo "📦 安装 PostgreSQL..."
brew install postgresql@15

echo "🔧 启动 PostgreSQL 服务..."
brew services start postgresql@15

# 等待服务启动
echo "⏳ 等待 PostgreSQL 服务启动..."
sleep 5

echo "🗄️ 创建数据库和用户..."

# 创建 testdb 数据库
createdb testdb 2>/dev/null || echo "数据库 testdb 可能已存在"

# 连接到 testdb 并执行初始化
psql testdb << 'EOF'
-- 创建UUID扩展（如果需要）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 输出初始化完成信息
SELECT 'AI Agent数据库初始化完成' as status;
EOF

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL 安装和配置完成！"
    echo ""
    echo "📋 数据库连接信息："
    echo "  主机: localhost (127.0.0.1)"
    echo "  端口: 5432"
    echo "  数据库: testdb"
    echo "  用户名: $(whoami)"
    echo "  密码: (无密码)"
    echo ""
    echo "🔧 管理命令："
    echo "  启动服务: brew services start postgresql@15"
    echo "  停止服务: brew services stop postgresql@15"
    echo "  重启服务: brew services restart postgresql@15"
    echo "  连接数据库: psql testdb"
    echo ""
    echo "⚠️  注意：您需要更新 application.yml 中的数据库配置："
    echo "  username: $(whoami)"
    echo "  password: (留空或删除此行)"
else
    echo "❌ 数据库初始化失败"
    exit 1
fi
