#!/bin/bash

echo "🔍 PostgreSQL 连接诊断工具"
echo "================================"

# 设置环境变量
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin

echo ""
echo "1. 检查 PostgreSQL 服务状态..."
brew services list | grep postgresql

echo ""
echo "2. 检查端口占用情况..."
lsof -i :5432 2>/dev/null || echo "端口 5432 未被占用"

echo ""
echo "3. 测试基本连接..."
if psql -h localhost -U sunyilin testdb -c "SELECT 'Connection successful!' as status;" 2>/dev/null; then
    echo "✅ 基本连接测试成功"
else
    echo "❌ 基本连接测试失败"
fi

echo ""
echo "4. 检查数据库配置..."
echo "监听地址和端口:"
psql -h localhost -U sunyilin testdb -c "SHOW listen_addresses; SHOW port;" 2>/dev/null

echo ""
echo "SSL 配置:"
psql -h localhost -U sunyilin testdb -c "SHOW ssl;" 2>/dev/null

echo ""
echo "5. 检查用户权限..."
psql -h localhost -U sunyilin testdb -c "SELECT current_user, current_database(), session_user;" 2>/dev/null

echo ""
echo "6. 检查数据库版本..."
psql -h localhost -U sunyilin testdb -c "SELECT version();" 2>/dev/null

echo ""
echo "7. 列出所有数据库..."
psql -h localhost -U sunyilin -l 2>/dev/null

echo ""
echo "8. 检查表结构..."
psql -h localhost -U sunyilin testdb -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null

echo ""
echo "9. 测试 JDBC 连接字符串..."
echo "推荐的 JDBC URL:"
echo "***************************************"
echo ""
echo "完整的 JDBC URL (包含认证):"
echo "***************************************?user=sunyilin&password=sunyilin&sslmode=disable"

echo ""
echo "10. 网络连接测试..."
if nc -z localhost 5432 2>/dev/null; then
    echo "✅ 端口 5432 可达"
else
    echo "❌ 端口 5432 不可达"
fi

echo ""
echo "================================"
echo "🎯 IDEA 连接配置建议:"
echo "Host: localhost"
echo "Port: 5432"
echo "Database: testdb"
echo "User: sunyilin"
echo "Password: sunyilin"
echo "SSL: disabled"
echo "================================"
