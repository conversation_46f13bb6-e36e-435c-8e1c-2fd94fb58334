#!/bin/bash

echo "🐳 部署AI Agent Docker环境"
echo "=========================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 1. 构建Java应用
echo ""
echo "🔨 构建Java应用..."

echo "构建MCP服务器..."
cd weather-mcp-server
if mvn clean package -DskipTests; then
    echo "✅ MCP服务器构建成功"
else
    echo "❌ MCP服务器构建失败"
    exit 1
fi
cd ..

echo "构建AI Agent核心..."
cd ai-agent-core
if mvn clean package -DskipTests; then
    echo "✅ AI Agent核心构建成功"
else
    echo "❌ AI Agent核心构建失败"
    exit 1
fi
cd ..

# 2. 停止现有容器
echo ""
echo "🛑 停止现有容器..."
docker-compose down

# 3. 构建Docker镜像
echo ""
echo "🏗️ 构建Docker镜像..."
docker-compose build

# 4. 启动服务
echo ""
echo "🚀 启动服务..."
docker-compose up -d

# 5. 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 30

# 6. 检查服务状态
echo ""
echo "🔍 检查服务状态..."

# 检查PostgreSQL
if docker-compose exec postgres pg_isready -U postgres -d testdb > /dev/null 2>&1; then
    echo "✅ PostgreSQL: 运行正常"
else
    echo "❌ PostgreSQL: 启动失败"
fi

# 检查MCP服务器
if curl -s http://localhost:8081/mcp/health > /dev/null; then
    echo "✅ MCP服务器: 运行正常"
else
    echo "❌ MCP服务器: 启动失败"
fi

# 检查AI Agent核心
if curl -s http://localhost:8080/api/agent/debug/tools > /dev/null; then
    echo "✅ AI Agent核心: 运行正常"
else
    echo "❌ AI Agent核心: 启动失败"
fi

# 7. 显示服务信息
echo ""
echo "📋 服务信息:"
echo "  PostgreSQL:    http://localhost:5432 (testdb/postgres/postgres)"
echo "  MCP服务器:     http://localhost:8081"
echo "  AI Agent核心:  http://localhost:8080"

echo ""
echo "🔗 可用的API端点:"
echo "  健康检查:      http://localhost:8080/api/agent/debug/tools"
echo "  对话接口:      http://localhost:8080/api/agent/chat"
echo "  流式接口:      http://localhost:8080/api/agent/chat/react-stream"
echo "  对话历史:      http://localhost:8080/api/conversation/stats/system"

echo ""
echo "📊 查看日志:"
echo "  docker-compose logs -f ai-agent-core"
echo "  docker-compose logs -f mcp-server"
echo "  docker-compose logs -f postgres"

echo ""
echo "🛑 停止服务:"
echo "  docker-compose down"

echo ""
echo "🎉 Docker部署完成！"
