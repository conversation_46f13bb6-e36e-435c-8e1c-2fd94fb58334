#!/usr/bin/env python3
"""
超简单的测试服务器
只使用 Python 标准库，不依赖外部包
"""

import json
import time
import uuid
import subprocess
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

# 设置 PostgreSQL 路径
os.environ['PATH'] = '/opt/homebrew/opt/postgresql@15/bin:' + os.environ.get('PATH', '')

class ChatHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理 GET 请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'success',
                'message': 'Simple AI Chat Test Server is running!',
                'timestamp': datetime.now().isoformat(),
                'endpoints': {
                    'chat': 'POST /chat',
                    'health': 'GET /health',
                    'test': 'GET /test'
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # 测试数据库连接
            db_status = self.test_database()
            
            response = {
                'status': 'UP' if db_status['connected'] else 'DOWN',
                'database': db_status,
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif path == '/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # 执行数据库测试
            test_results = self.run_database_tests()
            
            response = {
                'status': 'success',
                'tests': test_results,
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def do_POST(self):
        """处理 POST 请求"""
        if self.path == '/chat':
            self.handle_chat()
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def handle_chat(self):
        """处理聊天请求"""
        start_time = time.time()
        
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            message = data.get('message', '').strip()
            session_id = data.get('sessionId', f'session_{uuid.uuid4().hex[:8]}')
            
            if not message:
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                response = {'success': False, 'error': 'Message cannot be empty'}
                self.wfile.write(json.dumps(response).encode('utf-8'))
                return
            
            # 保存用户消息到数据库
            user_saved = self.save_message_to_db(session_id, 'user', message)
            
            # 生成响应
            ai_response = self.generate_response(message)
            
            # 计算响应时间
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # 保存助手响应到数据库
            assistant_saved = self.save_message_to_db(session_id, 'assistant', ai_response, response_time_ms)
            
            # 返回响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                'success': True,
                'response': ai_response,
                'sessionId': session_id,
                'responseTime': response_time_ms,
                'timestamp': datetime.now().isoformat(),
                'database': {
                    'user_message_saved': user_saved,
                    'assistant_message_saved': assistant_saved
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'success': False, 'error': f'Server error: {str(e)}'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def test_database(self):
        """测试数据库连接"""
        try:
            result = subprocess.run([
                'psql', 'testdb', '-c', 'SELECT version();'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                return {
                    'connected': True,
                    'version': result.stdout.strip().split('\n')[2] if len(result.stdout.strip().split('\n')) > 2 else 'Unknown'
                }
            else:
                return {
                    'connected': False,
                    'error': result.stderr.strip()
                }
        except Exception as e:
            return {
                'connected': False,
                'error': str(e)
            }
    
    def save_message_to_db(self, session_id, role, content, response_time_ms=None):
        """保存消息到数据库"""
        try:
            message_id = f'msg_{uuid.uuid4().hex[:8]}'
            
            # 首先确保会话存在
            session_cmd = f"""
            INSERT INTO agent_sessions (session_id, user_id, metadata)
            VALUES ('{session_id}', 'test_user', '{{"source": "python_simple"}}')
            ON CONFLICT (session_id) DO NOTHING;
            """
            
            subprocess.run([
                'psql', 'testdb', '-c', session_cmd
            ], capture_output=True, text=True, timeout=5)
            
            # 保存消息
            message_cmd = f"""
            INSERT INTO conversation_history (
                session_id, message_id, role, content, 
                cognitive_architecture, processing_mode, response_time_ms
            ) VALUES (
                '{session_id}', '{message_id}', '{role}', '{content.replace("'", "''")}',
                'SimpleTest', 'standard', {response_time_ms or 'NULL'}
            );
            """
            
            result = subprocess.run([
                'psql', 'testdb', '-c', message_cmd
            ], capture_output=True, text=True, timeout=5)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"保存消息失败: {e}")
            return False
    
    def run_database_tests(self):
        """运行数据库测试"""
        tests = []
        
        # 测试1: 连接测试
        db_status = self.test_database()
        tests.append({
            'name': 'Database Connection',
            'passed': db_status['connected'],
            'details': db_status
        })
        
        # 测试2: 表存在性测试
        try:
            result = subprocess.run([
                'psql', 'testdb', '-c', 
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('agent_sessions', 'conversation_history');"
            ], capture_output=True, text=True, timeout=5)
            
            table_count = int(result.stdout.strip().split('\n')[2].strip()) if result.returncode == 0 else 0
            tests.append({
                'name': 'Required Tables Exist',
                'passed': table_count == 2,
                'details': f'Found {table_count}/2 required tables'
            })
        except Exception as e:
            tests.append({
                'name': 'Required Tables Exist',
                'passed': False,
                'details': f'Error: {str(e)}'
            })
        
        # 测试3: 数据插入测试
        test_session = f'test_session_{int(time.time())}'
        insert_success = self.save_message_to_db(test_session, 'user', 'Test message')
        tests.append({
            'name': 'Data Insert',
            'passed': insert_success,
            'details': f'Test session: {test_session}'
        })
        
        # 测试4: 数据查询测试
        try:
            result = subprocess.run([
                'psql', 'testdb', '-c', 
                f"SELECT COUNT(*) FROM conversation_history WHERE session_id = '{test_session}';"
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                count = int(result.stdout.strip().split('\n')[2].strip())
                tests.append({
                    'name': 'Data Query',
                    'passed': count > 0,
                    'details': f'Found {count} messages for test session'
                })
            else:
                tests.append({
                    'name': 'Data Query',
                    'passed': False,
                    'details': 'Query failed'
                })
        except Exception as e:
            tests.append({
                'name': 'Data Query',
                'passed': False,
                'details': f'Error: {str(e)}'
            })
        
        return tests
    
    def generate_response(self, message):
        """生成简单的响应"""
        message_lower = message.lower()
        
        if '你好' in message or 'hello' in message_lower:
            return "你好！我是 AI Agent 测试服务器。我可以帮助您测试对话功能和数据库连接。"
        elif '天气' in message:
            return "抱歉，我目前无法获取实时天气信息。这是一个测试服务器，主要用于验证数据库和基本对话功能。"
        elif '时间' in message:
            return f"当前时间是：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        elif '测试' in message:
            return "测试功能正常！数据库连接正常，消息已成功保存到 PostgreSQL 数据库中。"
        elif '介绍' in message or '你是谁' in message:
            return """我是一个简单的 AI 聊天测试服务器，基于 Python 标准库构建。

我的功能包括：
1. 接收和响应消息
2. 将对话保存到 PostgreSQL 数据库
3. 提供健康检查和测试接口
4. 验证数据库连接和基本 CRUD 操作

这个服务器主要用于测试您的 AI Agent 框架的数据库配置和基本功能。"""
        elif '数据库' in message:
            db_status = self.test_database()
            if db_status['connected']:
                return f"数据库连接正常！\n数据库版本：{db_status.get('version', 'Unknown')}\n所有消息都会保存到 testdb 数据库中。"
            else:
                return f"数据库连接失败：{db_status.get('error', 'Unknown error')}"
        else:
            return f"我收到了您的消息：「{message}」\n\n这是一个测试响应，表明系统运行正常。消息已保存到数据库中。您可以发送以下关键词获取特定响应：你好、时间、测试、介绍、数据库。"
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server():
    """启动服务器"""
    server_address = ('', 8888)
    httpd = HTTPServer(server_address, ChatHandler)

    print("🚀 简单 AI 聊天测试服务器启动成功！")
    print("")
    print("📋 服务信息:")
    print("  地址: http://localhost:8888")
    print("  数据库: PostgreSQL (testdb)")
    print("")
    print("🧪 测试 URL:")
    print("  首页: http://localhost:8888/")
    print("  健康检查: http://localhost:8888/health")
    print("  数据库测试: http://localhost:8888/test")
    print("")
    print("📝 聊天测试:")
    print("  curl -X POST http://localhost:8888/chat \\")
    print("    -H 'Content-Type: application/json' \\")
    print("    -d '{\"message\":\"你好\",\"sessionId\":\"test\"}'")
    print("")
    print("⏹️  按 Ctrl+C 停止服务")
    print("")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
