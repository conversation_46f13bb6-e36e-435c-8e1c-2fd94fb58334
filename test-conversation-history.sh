#!/bin/bash

echo "🧪 测试对话历史记录功能"
echo "======================="

BASE_URL="http://localhost:8080"

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s "$BASE_URL/api/agent/debug/tools" > /dev/null; then
    echo "❌ AI Agent服务未运行，请先启动服务"
    exit 1
fi
echo "✅ AI Agent服务正在运行"

# 生成测试会话ID
SESSION_ID="test_session_$(date +%s)"
USER_ID="test_user_001"

echo ""
echo "📋 测试配置:"
echo "  会话ID: $SESSION_ID"
echo "  用户ID: $USER_ID"
echo "  基础URL: $BASE_URL"

# 1. 发送几条测试消息
echo ""
echo "1️⃣ 发送测试对话..."

echo "发送消息1: 你好"
curl -s -X POST "$BASE_URL/api/agent/chat" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -H "X-User-ID: $USER_ID" \
  -d '{
    "message": "你好",
    "mode": "SIMPLE"
  }' > /dev/null

sleep 2

echo "发送消息2: 北京天气如何"
curl -s -X POST "$BASE_URL/api/agent/chat" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -H "X-User-ID: $USER_ID" \
  -d '{
    "message": "北京天气如何",
    "mode": "REACT"
  }' > /dev/null

sleep 2

echo "发送消息3: 计算 15 + 25"
curl -s -X POST "$BASE_URL/api/agent/chat" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -H "X-User-ID: $USER_ID" \
  -d '{
    "message": "计算 15 + 25",
    "mode": "FLEXIBLE"
  }' > /dev/null

echo "✅ 测试对话发送完成"

# 2. 查询会话历史
echo ""
echo "2️⃣ 查询会话历史..."
SESSION_HISTORY=$(curl -s "$BASE_URL/api/conversation/session/$SESSION_ID")
MESSAGE_COUNT=$(echo "$SESSION_HISTORY" | jq '. | length')
echo "会话消息数量: $MESSAGE_COUNT"

if [ "$MESSAGE_COUNT" -gt 0 ]; then
    echo "✅ 会话历史记录正常"
    echo "最新消息类型: $(echo "$SESSION_HISTORY" | jq -r '.[-1].messageType')"
    echo "最新消息时间: $(echo "$SESSION_HISTORY" | jq -r '.[-1].createdAt')"
else
    echo "❌ 未找到会话历史记录"
fi

# 3. 查询用户历史
echo ""
echo "3️⃣ 查询用户历史..."
USER_HISTORY=$(curl -s "$BASE_URL/api/conversation/user/$USER_ID?page=0&size=10")
TOTAL_MESSAGES=$(echo "$USER_HISTORY" | jq '.totalElements')
echo "用户总消息数: $TOTAL_MESSAGES"

if [ "$TOTAL_MESSAGES" -gt 0 ]; then
    echo "✅ 用户历史记录正常"
else
    echo "❌ 未找到用户历史记录"
fi

# 4. 查询会话统计
echo ""
echo "4️⃣ 查询会话统计..."
SESSION_STATS=$(curl -s "$BASE_URL/api/conversation/session/$SESSION_ID/stats")
echo "会话统计信息:"
echo "$SESSION_STATS" | jq '.'

# 5. 查询用户最近对话
echo ""
echo "5️⃣ 查询用户最近对话..."
RECENT_CONVERSATIONS=$(curl -s "$BASE_URL/api/conversation/user/$USER_ID/recent?limit=5")
RECENT_COUNT=$(echo "$RECENT_CONVERSATIONS" | jq '. | length')
echo "最近对话数量: $RECENT_COUNT"

# 6. 查询用户会话列表
echo ""
echo "6️⃣ 查询用户会话列表..."
USER_SESSIONS=$(curl -s "$BASE_URL/api/conversation/user/$USER_ID/sessions")
SESSION_COUNT=$(echo "$USER_SESSIONS" | jq '. | length')
echo "用户会话数量: $SESSION_COUNT"
echo "会话ID列表: $(echo "$USER_SESSIONS" | jq -r '.[]' | head -3)"

# 7. 查询认知架构统计
echo ""
echo "7️⃣ 查询认知架构统计..."
COGNITIVE_STATS=$(curl -s "$BASE_URL/api/conversation/stats/cognitive-architecture")
echo "认知架构使用统计:"
echo "$COGNITIVE_STATS" | jq '.'

# 8. 搜索对话
echo ""
echo "8️⃣ 搜索对话..."
SEARCH_RESULTS=$(curl -s "$BASE_URL/api/conversation/search?userId=$USER_ID&page=0&size=5")
SEARCH_COUNT=$(echo "$SEARCH_RESULTS" | jq '. | length')
echo "搜索结果数量: $SEARCH_COUNT"

# 9. 查询系统统计
echo ""
echo "9️⃣ 查询系统统计..."
SYSTEM_STATS=$(curl -s "$BASE_URL/api/conversation/stats/system")
echo "系统统计信息:"
echo "$SYSTEM_STATS" | jq '.'

echo ""
echo "🎯 测试结果总结:"
echo "================"
echo "✅ 会话历史记录: $MESSAGE_COUNT 条消息"
echo "✅ 用户历史记录: $TOTAL_MESSAGES 条总消息"
echo "✅ 最近对话: $RECENT_COUNT 条"
echo "✅ 用户会话: $SESSION_COUNT 个会话"
echo "✅ 搜索功能: $SEARCH_COUNT 条结果"

if [ "$MESSAGE_COUNT" -gt 0 ] && [ "$TOTAL_MESSAGES" -gt 0 ]; then
    echo ""
    echo "🎉 对话历史记录功能测试通过！"
    echo "📊 数据库成功记录了所有对话信息"
    echo "🔍 API接口全部正常工作"
else
    echo ""
    echo "❌ 对话历史记录功能测试失败"
    echo "请检查数据库连接和配置"
fi

echo ""
echo "📋 可用的API端点:"
echo "  GET  /api/conversation/session/{sessionId}"
echo "  GET  /api/conversation/user/{userId}"
echo "  GET  /api/conversation/user/{userId}/recent"
echo "  GET  /api/conversation/user/{userId}/sessions"
echo "  GET  /api/conversation/session/{sessionId}/stats"
echo "  GET  /api/conversation/stats/cognitive-architecture"
echo "  GET  /api/conversation/search"
echo "  GET  /api/conversation/stats/system"
