#!/usr/bin/env python3
"""
带密码认证的简单测试服务器
"""

import json
import time
import uuid
import subprocess
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# 设置 PostgreSQL 路径和密码
os.environ['PATH'] = '/opt/homebrew/opt/postgresql@15/bin:' + os.environ.get('PATH', '')
os.environ['PGPASSWORD'] = 'sunyilin'

class ChatHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理 GET 请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'success',
                'message': 'AI Chat Test Server with Password Auth is running!',
                'timestamp': datetime.now().isoformat(),
                'database': 'PostgreSQL with password authentication',
                'endpoints': {
                    'chat': 'POST /chat',
                    'health': 'GET /health',
                    'test': 'GET /test'
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # 测试数据库连接
            db_status = self.test_database()
            
            response = {
                'status': 'UP' if db_status['connected'] else 'DOWN',
                'database': db_status,
                'authentication': 'Password-based',
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif path == '/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # 执行数据库测试
            test_results = self.run_database_tests()
            
            response = {
                'status': 'success',
                'tests': test_results,
                'authentication': 'Password-based',
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def do_POST(self):
        """处理 POST 请求"""
        if self.path == '/chat':
            self.handle_chat()
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'error': 'Not found'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def handle_chat(self):
        """处理聊天请求"""
        start_time = time.time()
        
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            message = data.get('message', '').strip()
            session_id = data.get('sessionId', f'session_{uuid.uuid4().hex[:8]}')
            
            if not message:
                self.send_response(400)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                response = {'success': False, 'error': 'Message cannot be empty'}
                self.wfile.write(json.dumps(response).encode('utf-8'))
                return
            
            # 保存用户消息到数据库
            user_saved = self.save_message_to_db(session_id, 'user', message)
            
            # 生成响应
            ai_response = self.generate_response(message)
            
            # 计算响应时间
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # 保存助手响应到数据库
            assistant_saved = self.save_message_to_db(session_id, 'assistant', ai_response, response_time_ms)
            
            # 返回响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            response = {
                'success': True,
                'response': ai_response,
                'sessionId': session_id,
                'responseTime': response_time_ms,
                'timestamp': datetime.now().isoformat(),
                'database': {
                    'user_message_saved': user_saved,
                    'assistant_message_saved': assistant_saved,
                    'authentication': 'password-based'
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {'success': False, 'error': f'Server error: {str(e)}'}
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def test_database(self):
        """测试数据库连接（使用密码）"""
        try:
            result = subprocess.run([
                'psql', '-h', 'localhost', '-U', 'sunyilin', 'testdb', '-c', 'SELECT version();'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                return {
                    'connected': True,
                    'version': result.stdout.strip().split('\n')[2] if len(result.stdout.strip().split('\n')) > 2 else 'Unknown',
                    'auth_method': 'password'
                }
            else:
                return {
                    'connected': False,
                    'error': result.stderr.strip(),
                    'auth_method': 'password'
                }
        except Exception as e:
            return {
                'connected': False,
                'error': str(e),
                'auth_method': 'password'
            }
    
    def save_message_to_db(self, session_id, role, content, response_time_ms=None):
        """保存消息到数据库（使用密码）"""
        try:
            message_id = f'msg_{uuid.uuid4().hex[:8]}'
            
            # 首先确保会话存在
            session_cmd = f"""
            INSERT INTO agent_sessions (session_id, user_id, metadata)
            VALUES ('{session_id}', 'test_user', '{{"source": "python_with_password"}}')
            ON CONFLICT (session_id) DO NOTHING;
            """
            
            subprocess.run([
                'psql', '-h', 'localhost', '-U', 'sunyilin', 'testdb', '-c', session_cmd
            ], capture_output=True, text=True, timeout=5)
            
            # 保存消息
            message_cmd = f"""
            INSERT INTO conversation_history (
                session_id, message_id, role, content, 
                cognitive_architecture, processing_mode, response_time_ms
            ) VALUES (
                '{session_id}', '{message_id}', '{role}', '{content.replace("'", "''")}',
                'PasswordAuth', 'standard', {response_time_ms or 'NULL'}
            );
            """
            
            result = subprocess.run([
                'psql', '-h', 'localhost', '-U', 'sunyilin', 'testdb', '-c', message_cmd
            ], capture_output=True, text=True, timeout=5)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"保存消息失败: {e}")
            return False
    
    def run_database_tests(self):
        """运行数据库测试（使用密码）"""
        tests = []
        
        # 测试1: 连接测试
        db_status = self.test_database()
        tests.append({
            'name': 'Database Connection (Password Auth)',
            'passed': db_status['connected'],
            'details': db_status
        })
        
        # 测试2: 表存在性测试
        try:
            result = subprocess.run([
                'psql', '-h', 'localhost', '-U', 'sunyilin', 'testdb', '-c', 
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('agent_sessions', 'conversation_history');"
            ], capture_output=True, text=True, timeout=5)
            
            table_count = int(result.stdout.strip().split('\n')[2].strip()) if result.returncode == 0 else 0
            tests.append({
                'name': 'Required Tables Exist',
                'passed': table_count == 2,
                'details': f'Found {table_count}/2 required tables'
            })
        except Exception as e:
            tests.append({
                'name': 'Required Tables Exist',
                'passed': False,
                'details': f'Error: {str(e)}'
            })
        
        # 测试3: 密码认证测试
        test_session = f'password_test_{int(time.time())}'
        insert_success = self.save_message_to_db(test_session, 'user', 'Password auth test message')
        tests.append({
            'name': 'Password Authentication & Data Insert',
            'passed': insert_success,
            'details': f'Test session: {test_session}'
        })
        
        return tests
    
    def generate_response(self, message):
        """生成简单的响应"""
        message_lower = message.lower()
        
        if '你好' in message or 'hello' in message_lower:
            return "你好！我是带密码认证的 AI Agent 测试服务器。数据库连接使用密码认证，更加安全！"
        elif '密码' in message or 'password' in message_lower:
            return "是的，我现在使用密码认证连接 PostgreSQL 数据库。用户名：sunyilin，密码：sunyilin。这提供了更好的安全性。"
        elif '测试' in message:
            return "密码认证测试功能正常！数据库连接安全，消息已成功保存到 PostgreSQL 数据库中。"
        elif '介绍' in message or '你是谁' in message:
            return """我是一个带密码认证的 AI 聊天测试服务器！

新特性：
✅ 使用密码认证连接 PostgreSQL 数据库
✅ 更安全的数据库访问控制
✅ 完整的 CRUD 操作测试
✅ 密码认证状态监控

数据库配置：
- 用户名：sunyilin
- 密码：sunyilin
- 数据库：testdb
- 认证方式：密码认证"""
        else:
            return f"我收到了您的消息：「{message}」\n\n✅ 使用密码认证连接数据库\n✅ 消息已安全保存\n✅ 系统运行正常"
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server():
    """启动服务器"""
    server_address = ('', 8889)
    httpd = HTTPServer(server_address, ChatHandler)
    
    print("🚀 带密码认证的 AI 聊天测试服务器启动成功！")
    print("")
    print("📋 服务信息:")
    print("  地址: http://localhost:8889")
    print("  数据库: PostgreSQL (testdb)")
    print("  认证: 密码认证 (用户名: sunyilin, 密码: sunyilin)")
    print("")
    print("🧪 测试 URL:")
    print("  首页: http://localhost:8889/")
    print("  健康检查: http://localhost:8889/health")
    print("  数据库测试: http://localhost:8889/test")
    print("")
    print("📝 聊天测试:")
    print("  curl -X POST http://localhost:8889/chat \\")
    print("    -H 'Content-Type: application/json' \\")
    print("    -d '{\"message\":\"测试密码认证\",\"sessionId\":\"test\"}'")
    print("")
    print("⏹️  按 Ctrl+C 停止服务")
    print("")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
