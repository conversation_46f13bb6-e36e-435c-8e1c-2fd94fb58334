#!/bin/bash

echo "🚀 启动简单 AI 聊天服务器..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 1. 检查 PostgreSQL 服务
echo "🔍 检查 PostgreSQL 服务..."
if ! psql testdb -c "SELECT 1;" >/dev/null 2>&1; then
    echo "⚠️  PostgreSQL 服务未运行，正在启动..."
    brew services start postgresql@15
    sleep 3
    
    if ! psql testdb -c "SELECT 1;" >/dev/null 2>&1; then
        echo "❌ PostgreSQL 服务启动失败"
        exit 1
    fi
fi
echo "✅ PostgreSQL 服务正常"

# 2. 检查 Python 环境
echo "🔍 检查 Python 环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到 Python3，请先安装 Python"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
echo "✅ Python 环境: $PYTHON_VERSION"

# 3. 安装 Python 依赖
echo "📦 安装 Python 依赖..."
pip3 install flask psycopg2-binary requests --quiet

if [ $? -ne 0 ]; then
    echo "❌ Python 依赖安装失败"
    exit 1
fi
echo "✅ Python 依赖安装成功"

# 4. 检查数据库表是否存在
echo "🔍 检查数据库表..."
if ! psql testdb -c "SELECT 1 FROM agent_sessions LIMIT 1;" >/dev/null 2>&1; then
    echo "⚠️  数据库表不存在，正在创建..."
    ./test-database-only.sh >/dev/null 2>&1
    
    if [ $? -ne 0 ]; then
        echo "❌ 数据库表创建失败"
        exit 1
    fi
fi
echo "✅ 数据库表检查完成"

# 5. 启动聊天服务器
echo ""
echo "🚀 启动 AI 聊天服务器..."
echo "📝 服务将在端口 5000 启动"
echo "📝 按 Ctrl+C 停止服务"
echo ""

# 启动服务器
python3 simple-ai-chat-server.py
