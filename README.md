# AI Agent Framework

一个基于Spring Boot的智能Agent框架，支持ReAct推理和Function Calling两种执行模式。

## 🚀 项目特性

- **双模式支持**：ReAct推理模式 + Function Calling模式
- **AI模型集成**：支持阿里云通义千问大模型
- **函数调用**：内置数学计算、时间查询、天气查询等功能
- **MCP协议**：支持Model Context Protocol
- **RESTful API**：提供标准的HTTP接口
- **高度可扩展**：模块化设计，易于扩展新功能

## 📋 目录

- [快速开始](#快速开始)
- [架构设计](#架构设计)
- [两种模式详解](#两种模式详解)
- [API使用](#api使用)
- [配置说明](#配置说明)
- [扩展开发](#扩展开发)

## 🏃 快速开始

### 环境要求

- Java 17+
- Maven 3.6+
- Spring Boot 3.x

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd test-agent3



