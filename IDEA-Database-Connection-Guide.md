# IntelliJ IDEA PostgreSQL 数据库连接配置指南

## 🔧 连接配置

### 基本信息
- **数据库类型**: PostgreSQL
- **主机**: `localhost` 或 `127.0.0.1`
- **端口**: `5432`
- **数据库**: `testdb`
- **用户名**: `sunyilin`
- **密码**: `sunyilin`

### 详细配置步骤

#### 1. 打开数据库工具窗口
- 在 IDEA 中按 `Ctrl+Shift+A` (Windows/Linux) 或 `Cmd+Shift+A` (macOS)
- 搜索 "Database" 并打开 Database 工具窗口
- 或者通过 `View` → `Tool Windows` → `Database`

#### 2. 添加新的数据源
- 点击 Database 工具窗口中的 `+` 按钮
- 选择 `Data Source` → `PostgreSQL`

#### 3. 配置连接参数
```
Host: localhost
Port: 5432
Database: testdb
User: sunyilin
Password: sunyilin
```

#### 4. 高级设置
- **SSL**: 禁用 (我们的本地数据库没有启用 SSL)
- **Schema**: public (默认)
- **URL**: `***************************************`

#### 5. 驱动程序
- IDEA 会自动下载 PostgreSQL JDBC 驱动
- 如果提示下载驱动，请点击 "Download missing driver files"

#### 6. 测试连接
- 点击 "Test Connection" 按钮
- 应该显示 "Successful" 消息

## 🚨 常见问题解决

### 问题 1: "Connection refused"
**解决方案**: 确保 PostgreSQL 服务正在运行
```bash
brew services start postgresql@15
```

### 问题 2: "Authentication failed"
**解决方案**: 检查用户名和密码
- 用户名: `sunyilin`
- 密码: `sunyilin`

### 问题 3: "Database does not exist"
**解决方案**: 确保数据库存在
```bash
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin
psql -h localhost -U sunyilin -l
```

### 问题 4: "Driver not found"
**解决方案**: 
1. 在连接配置中点击 "Download missing driver files"
2. 或者手动指定 PostgreSQL JDBC 驱动路径

### 问题 5: "SSL connection required"
**解决方案**: 在高级设置中禁用 SSL
- 在连接配置的 "Advanced" 选项卡中
- 设置 `sslmode=disable`

## 🔍 验证连接

### 方法 1: 使用命令行验证
```bash
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin
psql -h localhost -U sunyilin testdb -c "SELECT version();"
```

### 方法 2: 检查服务状态
```bash
brew services list | grep postgresql
```

### 方法 3: 检查端口占用
```bash
lsof -i :5432
```

## 📝 JDBC URL 格式

完整的 JDBC URL 应该是：
```
***************************************************************************************
```

## 🛠️ 故障排除命令

如果仍然无法连接，请运行以下诊断命令：

```bash
# 检查 PostgreSQL 服务状态
brew services list | grep postgresql

# 检查数据库连接
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
export PGPASSWORD=sunyilin
psql -h localhost -U sunyilin testdb -c "SELECT 'Connection OK' as status;"

# 检查监听地址和端口
psql -h localhost -U sunyilin testdb -c "SHOW listen_addresses; SHOW port;"

# 检查用户权限
psql -h localhost -U sunyilin testdb -c "SELECT current_user, current_database();"
```

## 📊 数据库表结构

连接成功后，您应该能看到以下表：
- `agent_sessions` - 会话信息
- `conversation_history` - 对话历史

## 🎯 测试查询

连接成功后，可以运行以下测试查询：
```sql
-- 查看所有表
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- 查看会话数据
SELECT * FROM agent_sessions LIMIT 5;

-- 查看对话历史
SELECT * FROM conversation_history ORDER BY created_at DESC LIMIT 10;
```
