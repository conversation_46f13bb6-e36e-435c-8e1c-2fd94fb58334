#!/bin/bash

echo "🚀 启动本地 AI Agent 服务（不使用 Docker）..."

# 确保 PostgreSQL 在 PATH 中
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 检查 PostgreSQL 服务状态
echo "🔍 检查 PostgreSQL 服务状态..."
if ! psql testdb -c "SELECT 'PostgreSQL 运行正常' as status;" >/dev/null 2>&1; then
    echo "⚠️  PostgreSQL 服务未运行，正在启动..."
    brew services start postgresql@15
    sleep 3
    
    if ! psql testdb -c "SELECT 'PostgreSQL 运行正常' as status;" >/dev/null 2>&1; then
        echo "❌ PostgreSQL 启动失败，请检查服务状态"
        exit 1
    fi
fi

echo "✅ PostgreSQL 服务运行正常"

# 检查 Java 环境
echo "🔍 检查 Java 环境..."
if ! java -version >/dev/null 2>&1; then
    echo "❌ 未找到 Java，请确保已安装 Java 17 或更高版本"
    exit 1
fi

echo "✅ Java 环境正常"

# 检查 Maven
echo "🔍 检查 Maven 环境..."
if ! mvn -version >/dev/null 2>&1; then
    echo "❌ 未找到 Maven，请确保已安装 Maven"
    exit 1
fi

echo "✅ Maven 环境正常"

# 进入 AI Agent 核心模块目录
cd ai-agent-core

# 停止可能运行的进程
echo "🧹 停止可能运行的 AI Agent 进程..."
pkill -f "ai-agent-core" 2>/dev/null || true

# 编译项目
echo "🏗️  编译 AI Agent 项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ 项目编译失败"
    exit 1
fi

echo "✅ 项目编译成功"

# 启动应用
echo "🚀 启动 AI Agent 应用..."
echo "📝 使用配置: application.yml (dev profile)"
echo ""

# 在后台启动应用
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > ../ai-agent.log 2>&1 &
APP_PID=$!

echo "🔄 应用启动中，PID: $APP_PID"
echo "📝 日志文件: ai-agent.log"

# 等待应用启动
echo "⏳ 等待应用启动..."
for i in {1..60}; do
    if curl -s http://localhost:8080/actuator/health >/dev/null 2>&1; then
        echo "✅ AI Agent 应用启动成功！"
        break
    fi
    
    if [ $i -eq 60 ]; then
        echo "❌ 应用启动超时"
        echo "📝 查看日志："
        tail -20 ../ai-agent.log
        kill $APP_PID 2>/dev/null
        exit 1
    fi
    
    if [ $((i % 10)) -eq 0 ]; then
        echo "等待中... ($i/60)"
    fi
    sleep 1
done

# 返回根目录
cd ..

# 显示服务信息
echo ""
echo "🎉 AI Agent 服务启动完成！"
echo ""
echo "📋 服务信息："
echo "  AI Agent API: http://localhost:8080"
echo "  健康检查: http://localhost:8080/actuator/health"
echo "  PostgreSQL: localhost:5432"
echo "  进程 PID: $APP_PID"
echo ""

# 测试健康检查
echo "🔍 测试健康检查..."
health_response=$(curl -s http://localhost:8080/actuator/health)
echo "健康状态: $health_response"
echo ""

# 测试工具端点
echo "🔧 测试工具端点..."
tools_response=$(curl -s http://localhost:8080/api/agent/debug/tools)
if [ $? -eq 0 ] && [ -n "$tools_response" ]; then
    echo "✅ 工具端点正常"
    echo "可用工具: $tools_response"
else
    echo "⚠️  工具端点可能未就绪"
fi
echo ""

# 测试 AI 对话功能
echo "🤖 测试 AI 对话功能..."
echo "📝 发送测试消息: '你好，请介绍一下你自己'"

response=$(curl -s -X POST http://localhost:8080/api/agent/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下你自己",
    "conversationId": "test-conversation-1"
  }')

if [ $? -eq 0 ] && [ -n "$response" ]; then
    echo "✅ AI 对话测试成功！"
    echo ""
    echo "🤖 AI 回复："
    echo "$response" | jq -r '.response // .message // .' 2>/dev/null || echo "$response"
    echo ""
else
    echo "❌ AI 对话测试失败"
    echo "响应: $response"
    echo ""
    echo "📝 查看最新日志："
    tail -10 ai-agent.log
fi

echo ""
echo "🔧 管理命令："
echo "  查看日志: tail -f ai-agent.log"
echo "  停止服务: kill $APP_PID"
echo "  重启服务: ./start-local-ai-agent.sh"
echo ""
echo "🧪 测试命令："
echo "  健康检查: curl http://localhost:8080/actuator/health"
echo "  工具列表: curl http://localhost:8080/api/agent/debug/tools"
echo "  发送消息: curl -X POST http://localhost:8080/api/agent/chat -H 'Content-Type: application/json' -d '{\"message\":\"你好\",\"conversationId\":\"test\"}'"
echo ""
echo "💡 提示: 应用在后台运行，PID: $APP_PID"
